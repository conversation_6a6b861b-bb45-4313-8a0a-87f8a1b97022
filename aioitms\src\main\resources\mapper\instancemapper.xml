<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.digiwin.escloud.aioitms.instance.dao.InstanceMapper">
    <resultMap id="snmpInstanceMap" type="com.digiwin.escloud.aioitms.instance.model.AiopsSNMPInstance">
        <id property="id" column="id"/>
        <result property="eid" column="eid"/>
        <result property="snmpType" column="snmpType"/>
        <result property="snmpId" column="snmpId"/>
        <result property="snmpName" column="snmpName"/>
        <result property="snmpIpAddress" column="snmpIpAddress"/>
        <result property="snmpPort" column="snmpPort"/>
        <result property="snmpPlacementPoint" column="snmpPlacementPoint"/>
        <result property="snmpRemark" column="snmpRemark"/>
        <result property="snmpCustomerNotes" column="snmpCustomerNotes"/>
        <result property="snmpVersion" column="snmpVersion"/>
        <result property="snmpCommunity" column="snmpCommunity"/>

        <result property="snmpContextName" column="snmpContextName"/>
        <result property="snmpSecurityLevel" column="snmpSecurityLevel"/>
        <result property="snmpUserName" column="snmpUserName"/>
        <result property="snmpAuthProtocol" column="snmpAuthProtocol"/>
        <result property="snmpAuthPassword" column="snmpAuthPassword"/>
        <result property="snmpPrivProtocol" column="snmpPrivProtocol"/>
        <result property="snmpPrivPassword" column="snmpPrivPassword"/>
    </resultMap>

    <resultMap id="deviceMap" type="com.digiwin.escloud.aioitms.model.device.AiopsKitDevice">
        <id column="id" property="id"/>
        <result column="eid" property="eid"/>
        <result column="deviceId" property="deviceId"/>
        <result column="deviceName" property="deviceName"/>
        <result column="platform" property="platform"/>
        <result column="ipAddress" property="ipAddress"/>
        <result column="placementPoint" property="placementPoint"/>
        <result column="remark" property="remark"/>
        <result column="aiopskitVersion" property="aiopskitVersion"/>
        <result column="escliVersion" property="escliVersion"/>
        <result column="registerTime" property="registerTime"/>
        <result column="lastCheckInTime" property="lastCheckInTime"/>
        <result column="isDeleted" property="isDeleted"/>

        <collection property="deviceTypeMappingList" columnPrefix="adtm_" resultMap="deviceTypeMappingMap"/>
    </resultMap>

    <resultMap id="deviceTypeMappingMap" type="com.digiwin.escloud.aioitms.model.device.AiopsKitDeviceTypeMapping">
        <id column="id" property="id"/>
        <result column="adId" property="adId"/>
        <result column="deviceId" property="deviceId"/>
        <result column="deviceType" property="deviceType"/>
    </resultMap>

    <resultMap id="deviceInfoMap" extends="deviceMap" type="com.digiwin.escloud.aioitms.device.model.DeviceInfo">
        <result column="isOnLine" property="isOnLine"/>
        <result column="lastCheckInTimeDifferSecond" property="lastCheckInTimeDifferSecond"/>

        <collection property="deviceTypeMappingList" columnPrefix="adtm_" resultMap="deviceTypeMappingMap"/>
    </resultMap>

    <resultMap id="snmpInfoMap" extends="snmpInstanceMap"
               type="com.digiwin.escloud.aioitms.instance.model.SNMPInfo">

        <result property="aiopsItemGroup" column="aiopsItemGroup"/>
        <result property="aiopsItemType" column="aiopsItemType"/>
        <result property="aiopsItemTypeName" column="aiopsItemTypeName"/>
        <result property="aiopsItemTypeName_CN" column="aiopsItemTypeName_CN"/>
        <result property="aiopsItemTypeName_TW" column="aiopsItemTypeName_TW"/>
        <result property="aiopsItem" column="aiopsItem"/>
        <result property="aiopsItemName" column="aiopsItemName"/>
        <result property="aiopsItemName_CN" column="aiopsItemName_CN"/>
        <result property="aiopsItemName_TW" column="aiopsItemName_TW"/>
        <result property="aiopsInstanceId" column="aiopsInstanceId"/>
        <result property="aiopsAuthStatus" column="aiopsAuthStatus"/>

        <collection property="aiopsKitDeviceList" columnPrefix="ad_" resultMap="deviceInfoMap"/>
    </resultMap>

<!--    <resultMap id="allMap"-->
<!--               type="com.digiwin.escloud.aioitms.device.model.WarningDeviceMap">-->

<!--        <collection property="aiopsKitDeviceList" columnPrefix="ad_" resultMap="deviceInfoMap"/>-->
<!--    </resultMap>-->

    <resultMap id="AiopsItemStatisticsMap" type="com.digiwin.escloud.aioitms.instance.model.AiopsItemStatistics">
        <result property="aiopsItem" column="aiopsItem"/>
        <result property="samcdId" column="samcdId"/>
        <result property="total" column="total"/>
        <result property="hasDevices" column="hasDevices"/>
        <result property="onlineCount" column="onlineCount"/>
    </resultMap>

    <resultMap id="mergeStatisticsDetailInfoMap"
               type="com.digiwin.escloud.aioitms.instance.model.MergeStatisticsDetailInfo">
        <result property="deviceName" column="deviceName"/>
        <result property="placementPoint" column="placementPoint"/>
        <result property="ipAddress" column="ipAddress"/>
        <result property="aiopsItemId" column="aiopsItemId"/>

        <result property="aiopsItemGroup" column="aiopsItemGroup"/>
        <result property="aiopsItemType" column="aiopsItemType"/>
        <result property="aiopsItemTypeName" column="aiopsItemTypeName"/>
        <result property="aiopsItemTypeName_CN" column="aiopsItemTypeName_CN"/>
        <result property="aiopsItemTypeName_TW" column="aiopsItemTypeName_TW"/>
        <result property="aiopsItem" column="aiopsItem"/>
        <result property="aiopsItemName" column="aiopsItemName"/>
        <result property="aiopsItemName_CN" column="aiopsItemName_CN"/>
        <result property="aiopsItemName_TW" column="aiopsItemName_TW"/>
        <result property="aiopsInstanceId" column="aiopsInstanceId"/>
        <result property="aiopsAuthStatus" column="aiopsAuthStatus"/>

        <result property="id" column="id"/>

        <collection property="aiopsKitDeviceList" columnPrefix="ad_" resultMap="deviceInfoMap"/>
    </resultMap>

    <sql id="getFindDisplayNameByModel">
        (CASE IFNULL(${aiAlias}.aiopsItemType, '') WHEN 'PRODUCT_APP' THEN 1
         ELSE 0 END)
    </sql>

    <sql id="getAiopsInstanceDisplayNameSql">
        IFNULL((CASE IFNULL(${aiAlias}.aiopsItemType, '') WHEN 'DATABASE' THEN
            (SELECT CASE WHEN dbType = 'mssql_v2' THEN
                        CASE WHEN IFNULL(dbInstanceName, '') = '' THEN IFNULL(dbIpAddress, '')
                        ELSE CONCAT(IFNULL(dbIpAddress, ''), '\\', IFNULL(dbInstanceName, ''))
                        END
                    WHEN dbType = 'oracle' THEN
                        CONCAT(IFNULL(dbIpAddress, ''), ':', IFNULL(dbPort, ''), '\\', IFNULL(dbServerName, ''))
                    ELSE CASE WHEN IFNULL(dbPort, '') = '' THEN IFNULL(dbIpAddress, '')
                         ELSE CONCAT(IFNULL(dbIpAddress, ''), ':', IFNULL(dbPort, ''))
                         END
                    END AS displayName
                FROM aiops_device_datasource
                WHERE dbId = ${aiAlias}.aiopsItemId
                LIMIT 1)
         WHEN 'SNMP' THEN
            (SELECT CONCAT(IFNULL(snmpName, ''), ' (', IFNULL(snmpIpAddress, ''), ':',
                    IFNULL(snmpPort, ''), ')')
             FROM aiops_snmp_instance
             WHERE snmpId = ${aiAlias}.aiopsItemId
             LIMIT 1)
         WHEN 'HTTP' THEN
            (SELECT CONCAT(IFNULL(apiDeviceName, ''), ' (', IFNULL(apiAddress, ''), ')')
             FROM aiops_http_instance
             WHERE apiCollectId = ${aiAlias}.aiopsItemId
             LIMIT 1)
         WHEN 'DEVICE' THEN
            (SELECT deviceName
            FROM aiops_device
            WHERE deviceId = ${aiAlias}.aiopsItemId
            LIMIT 1)
         WHEN 'BACKUP' THEN
            (SELECT backupSoftwareDisplayName
             FROM aiops_backup_software_instance
             WHERE backupSoftwareId = ${aiAlias}.aiopsItemId
             LIMIT 1)
         WHEN 'PRODUCT_APP' THEN
            (SELECT modelCode
             FROM aiops_device_product_mapping adpm
             INNER JOIN aiops_product_app apa ON adpm.apaId = apa.id
             WHERE adpm.appId = ${aiAlias}.aiopsItemId
             LIMIT 1)
         WHEN 'TMP_RH' THEN
            (select device_name from aiops_temp_rh_instance where tmp_rh_id = ${aiAlias}.aiopsItemId limit 1)
         WHEN 'EDR' THEN
            (SELECT CONCAT(edrDeviceName, '(', edrDeviceIpAddress, ')')
             FROM aiops_edr_instance
             WHERE edrId = ${aiAlias}.aiopsItemId
             LIMIT 1)

         WHEN 'SMART_METER' THEN
            (SELECT CONCAT(smartMeterDeviceName, '(', smartMeterDeviceId, ')')
             FROM aiops_smart_meter_instance
             WHERE smartMeterId = ${aiAlias}.aiopsItemId
             LIMIT 1)
        WHEN 'TP_PRODUCT_INTEGRATION' THEN
            (SELECT CONCAT(tpModule, '(', tpModule, ')')
            FROM aiops_tp_module_instance
            WHERE aiopsItemId = ${aiAlias}.aiopsItemId
            LIMIT 1)
         WHEN 'CLOUD_BACKUP' THEN
            (SELECT serviceCode
             FROM aiops_eai_cloud_instance
             WHERE eaiCloudId = ${aiAlias}.aiopsItemId
             LIMIT 1)
        WHEN 'DATA_SRV' THEN
            (SELECT apa.modelCode
            FROM aiops_device_product_mapping adpm
            INNER JOIN aiops_product_app apa ON adpm.apaId = apa.id
            WHERE adpm.appid = ${aiAlias}.aiopsItemId
            LIMIT 1)
        WHEN 'APP_AUTO_UPDATE' THEN
            (select ai.name
            from aiops_app_auto_update_instance aaaui
            inner join aiops_item ai on ai.code = aaaui.aiopsItem
            WHERE aaaui.aiopsItemId = ${aiAlias}.aiopsItemId
            LIMIT 1)
         ELSE '' END), '')
    </sql>

    <sql id="getAiopsInstanceWithIpDisplayNameSql">
        IFNULL((CASE IFNULL(${aiAlias}.aiopsItemType, '') WHEN 'DATABASE' THEN
            (SELECT CASE WHEN dbType = 'mssql_v2' THEN
                        CASE WHEN IFNULL(dbInstanceName, '') = '' THEN IFNULL(dbIpAddress, '')
                        ELSE CONCAT(IFNULL(dbIpAddress, ''), '\\', IFNULL(dbInstanceName, ''))
                        END
                    WHEN dbType = 'oracle' THEN
                        CONCAT(IFNULL(dbIpAddress, ''), ':', IFNULL(dbPort, ''), '\\', IFNULL(dbServerName, ''))
                    ELSE CASE WHEN IFNULL(dbPort, '') = '' THEN IFNULL(dbIpAddress, '')
                        ELSE CONCAT(IFNULL(dbIpAddress, ''), ':', IFNULL(dbPort, ''))
                        END
                    END AS displayName
             FROM aiops_device_datasource
             WHERE dbId = ${aiAlias}.aiopsItemId
             LIMIT 1)
        WHEN 'SNMP' THEN
            (SELECT CONCAT(IFNULL(snmpName, ''), ' (', IFNULL(snmpIpAddress, ''), ':',
                IFNULL(snmpPort, ''), ')')
             FROM aiops_snmp_instance
             WHERE snmpId = ${aiAlias}.aiopsItemId
             LIMIT 1)
        WHEN 'HTTP' THEN
            (SELECT CONCAT(IFNULL(apiDeviceName, ''), ' (', IFNULL(apiAddress, ''), ')')
             FROM aiops_http_instance
             WHERE apiCollectId = ${aiAlias}.aiopsItemId
             LIMIT 1)
        WHEN 'DEVICE' THEN
            (SELECT CONCAT(IFNULL(deviceName, ''), ' (', IFNULL(ipAddress, ''), ')')
             FROM aiops_device
             WHERE deviceId = ${aiAlias}.aiopsItemId
             LIMIT 1)
        WHEN 'BACKUP' THEN
            (SELECT backupSoftwareDisplayName
             FROM aiops_backup_software_instance
             WHERE backupSoftwareId = ${aiAlias}.aiopsItemId
             LIMIT 1)
        WHEN 'PRODUCT_APP' THEN
            (SELECT modelCode
             FROM aiops_device_product_mapping adpm
             INNER JOIN aiops_product_app apa ON adpm.apaId = apa.id
             WHERE adpm.appId = ${aiAlias}.aiopsItemId
             LIMIT 1)
        WHEN 'TMP_RH' THEN
            (SELECT device_name
             FROM aiops_temp_rh_instance
             WHERE tmp_rh_id = ${aiAlias}.aiopsItemId limit 1)
        WHEN 'EDR' THEN
            (SELECT CONCAT(edrDeviceName, '(', edrDeviceIpAddress, ')')
             FROM aiops_edr_instance
             WHERE edrId = ${aiAlias}.aiopsItemId
             LIMIT 1)
         WHEN 'SMART_METER' THEN
            (SELECT CONCAT(smartMeterDeviceName, '(', smartMeterDeviceId, ')')
             FROM aiops_smart_meter_instance
             WHERE smartMeterId = ${aiAlias}.aiopsItemId
             LIMIT 1)
         WHEN 'CLOUD_BACKUP' THEN
            (SELECT serviceCode
             FROM aiops_eai_cloud_instance
             WHERE eaiCloudId = ${aiAlias}.aiopsItemId
             LIMIT 1)
        ELSE '' END), '')
    </sql>

    <sql id="getAiopsInstanceWithNoIpDisplayNameSql">
        IFNULL((CASE IFNULL(${aiAlias}.aiopsItemType, '') WHEN 'DATABASE' THEN
            (SELECT CASE WHEN dbType = 'mssql_v2' THEN
                        CASE WHEN IFNULL(dbInstanceName, '') = '' THEN IFNULL(dbIpAddress, '')
                        ELSE CONCAT(IFNULL(dbIpAddress, ''), '\\', IFNULL(dbInstanceName, ''))
                        END
                    WHEN dbType = 'oracle' THEN
                        CONCAT(IFNULL(dbIpAddress, ''), ':', IFNULL(dbPort, ''), '\\', IFNULL(dbServerName, ''))
                    ELSE CASE WHEN IFNULL(dbPort, '') = '' THEN IFNULL(dbIpAddress, '')
                        ELSE CONCAT(IFNULL(dbIpAddress, ''), ':', IFNULL(dbPort, ''))
                        END
                    END AS displayName
            FROM aiops_device_datasource
            WHERE dbId = ${aiAlias}.aiopsItemId
            LIMIT 1)
        WHEN 'SNMP' THEN
            (SELECT snmpName
             FROM aiops_snmp_instance
             WHERE snmpId = ${aiAlias}.aiopsItemId
             LIMIT 1)
        WHEN 'HTTP' THEN
            (SELECT apiDeviceName
             FROM aiops_http_instance
             WHERE apiCollectId = ${aiAlias}.aiopsItemId
             LIMIT 1)
        WHEN 'DEVICE' THEN
            (SELECT deviceName
             FROM aiops_device
             WHERE deviceId = ${aiAlias}.aiopsItemId
             LIMIT 1)
        WHEN 'BACKUP' THEN
            (SELECT backupSoftwareDisplayName
             FROM aiops_backup_software_instance
             WHERE backupSoftwareId = ${aiAlias}.aiopsItemId
             LIMIT 1)
        WHEN 'PRODUCT_APP' THEN
            (SELECT modelCode
             FROM aiops_device_product_mapping adpm
             INNER JOIN aiops_product_app apa ON adpm.apaId = apa.id
             WHERE adpm.appId = ${aiAlias}.aiopsItemId
             LIMIT 1)
        WHEN 'TMP_RH' THEN
            (SELECT device_name
             FROM aiops_temp_rh_instance
             WHERE tmp_rh_id = ${aiAlias}.aiopsItemId limit 1)
        WHEN 'EDR' THEN
            (SELECT edrDeviceName
             FROM aiops_edr_instance
             WHERE edrId = ${aiAlias}.aiopsItemId
             LIMIT 1)
         WHEN 'SMART_METER' THEN
            (SELECT CONCAT(smartMeterDeviceName, '(', smartMeterDeviceId, ')')
             FROM aiops_smart_meter_instance
             WHERE smartMeterId = ${aiAlias}.aiopsItemId
             LIMIT 1)
        WHEN 'CLOUD_BACKUP' THEN
            (SELECT serviceCode
             FROM aiops_eai_cloud_instance
             WHERE eaiCloudId = ${aiAlias}.aiopsItemId
             LIMIT 1)
        ELSE '' END), '')
    </sql>

    <sql id="getAiopsInstancePlacePointSql">
        IFNULL((CASE IFNULL(${aiAlias}.aiopsItemType, '') WHEN 'SNMP' THEN
            (SELECT snmpPlacementPoint
             FROM aiops_snmp_instance
             WHERE snmpId = ${aiAlias}.aiopsItemId
             LIMIT 1)
        WHEN 'HTTP' THEN
            (SELECT apiDevicePlace
             FROM aiops_http_instance
             WHERE apiCollectId = ${aiAlias}.aiopsItemId
             LIMIT 1)
        WHEN 'DEVICE' THEN
            (SELECT placementPoint
             FROM aiops_device
             WHERE deviceId = ${aiAlias}.aiopsItemId
             LIMIT 1)
        WHEN 'TMP_RH' THEN
            (SELECT device_location as tmpPlacementPoint
            FROM (
                 select atri.tmp_rh_id, trci.device_location from
                 aiops_temp_rh_instance atri
                 inner join temp_rh_customer_info trci on atri.tmp_rh_id = trci.tmp_rh_id
            ) a WHERE tmp_rh_id = ${aiAlias}.aiopsItemId limit 1)
        ELSE '' END), '')
    </sql>

    <sql id="getAiopsInstanceIpAddressSql">
        IFNULL((CASE IFNULL(${aiAlias}.aiopsItemType, '') WHEN 'DATABASE' THEN
            (SELECT dbIpAddress
            FROM aiops_device_datasource
            WHERE dbId = ${aiAlias}.aiopsItemId
            LIMIT 1)
        WHEN 'SNMP' THEN
            (SELECT snmpIpAddress
             FROM aiops_snmp_instance
             WHERE snmpId = ${aiAlias}.aiopsItemId
             LIMIT 1)
        WHEN 'HTTP' THEN
            (SELECT ip
             FROM aiops_http_instance
             WHERE apiCollectId = ${aiAlias}.aiopsItemId
             LIMIT 1)
        WHEN 'DEVICE' THEN
            (SELECT ipAddress
             FROM aiops_device
             WHERE deviceId = ${aiAlias}.aiopsItemId
             LIMIT 1)
        WHEN 'EDR' THEN
            (SELECT edrDeviceIpAddress
             FROM aiops_edr_instance
             WHERE edrId = ${aiAlias}.aiopsItemId
             LIMIT 1)
        ELSE '' END), '')
    </sql>

    <sql id="getJoinAiopskitDeviceSql">
        LEFT JOIN (
            SELECT adim2.aiId,
                   ad.id AS ad_id,
                   ad.eid AS ad_eid,
                   ad.deviceId AS ad_deviceId,
                   ad.deviceName AS ad_deviceName,
                   ad.platform AS ad_platform,
                   ad.ipAddress AS ad_ipAddress,
                   ad.placementPoint AS ad_placementPoint,
                   ad.remark AS ad_remark,
                   ad.aiopskitVersion AS ad_aiopskitVersion,
                   ad.escliVersion AS ad_escliVersion,
                   ad.registerTime AS ad_registerTime,
                   ad.lastCheckInTime AS ad_lastCheckInTime,
                   ad.isDeleted AS ad_isDeleted,
                   ad.lastCheckInTime >= DATE_SUB(now(), INTERVAL 3 DAY) AS ad_isOnLine,
                   TIMESTAMPDIFF(SECOND, ad.lastCheckInTime, now()) AS ad_lastCheckInTimeDifferSecond,
                   adtm.id AS ad_adtm_id, adtm.adId AS ad_adtm_adId,
                   adtm.deviceId AS ad_adtm_deviceId, adtm.deviceType AS ad_adtm_deviceType
            FROM aiops_device_instance_mapping adim2
            INNER JOIN aiops_device ad ON adim2.adId = ad.id AND ad.eid = #{eid}
            LEFT JOIN aiops_device_type_mapping adtm ON ad.id = adtm.adId
        ) adim ON ${aiAlias}.id = adim.aiId
    </sql>

    <sql id="getJoinAiopsItemGroupSql">
        LEFT JOIN (
            SELECT aigm.aiopsItemGroup, aigm.aiopsItem
            FROM aiops_item_group_mapping aigm
            INNER JOIN aiops_item_group aig ON aigm.aiopsItemGroup = aig.code
            WHERE aigm.isDefault = 1
            GROUP BY aigm.aiopsItem
        ) aigm ON ${aiAlias}.aiopsItem = aigm.aiopsItem
    </sql>

    <select id="selectAiopsItemByCodeList" resultType="com.digiwin.escloud.aioitms.instance.model.AiopsItem">
        SELECT *
        FROM aiops_item
        WHERE 1 = 1
        <if test="codeList != null">
            <foreach collection="codeList" item="item" open=" AND code IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <delete id="deleteAiopsInstanceByAiIdList">
        DELETE FROM aiops_instance
        WHERE 1 != 1
        <if test="aiIdList != null">
            <foreach collection="aiIdList" item="item" open=" OR id IN(" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </delete>

    <update id="batchUpdateAiopsInstanceSamcdIdAndAiopsItemIdByAicList"
            parameterType="com.digiwin.escloud.aioitms.instance.model.AiopsItemContext">
        UPDATE aiops_instance
        SET id = id
        <if test="aicList != null">
            <foreach collection="aicList" item="item"
                     open=", samcdId = CASE id WHEN " separator=" WHEN " close=" END">
                #{item.aiId} THEN #{item.samcdId}
            </foreach>
            <foreach collection="aicList" item="item"
                     open=", aiopsItemType = CASE id WHEN " separator=" WHEN " close=" END">
                #{item.aiId} THEN #{item.aiopsItemType}
            </foreach>
            <foreach collection="aicList" item="item"
                     open=", aiopsItem = CASE id WHEN " separator=" WHEN " close=" END">
                #{item.aiId} THEN #{item.aiopsItem}
            </foreach>
            <foreach collection="aicList" item="item"
                     open=", aiopsItemId = CASE id WHEN " separator=" WHEN " close=" END">
                #{item.aiId} THEN #{item.aiopsItemId}
            </foreach>
            <foreach collection="aicList" item="item"
                     open=", independentAuth = CASE id WHEN " separator=" WHEN " close=" END">
                #{item.aiId} THEN #{item.independentAuth}
            </foreach>
        </if>
        WHERE 1 != 1
        <if test="aicList != null">
            <foreach collection="aicList" item="item" open=" OR id IN (" separator=", " close=")">
                #{item.aiId}
            </foreach>
        </if>
    </update>

    <select id="selectAiopsInstanceExistByAicList"
            resultType="com.digiwin.escloud.aioitms.instance.model.AiopsInstance">
        SELECT *
        FROM aiops_instance
        WHERE 1 = 1
        <if test="aicList != null">
            <foreach collection="aicList" item="item" open="AND ((" separator=") OR (" close="))">
                samcdId = #{item.samcdId} AND aiopsItemId = #{item.aiopsItemId}
            </foreach>
        </if>
        <if test="aiopsAuthStatus != null and aiopsAuthStatus != ''">
            AND aiopsAuthStatus = #{aiopsAuthStatus}
        </if>
    </select>

    <select id="selectAiopsSnmpInstanceAgentDevice" resultMap="snmpInfoMap">
        SELECT asi.*,
               ai.aiopsItemType, ai.aiopsItem,ai.id aiopsInstanceId,
               IFNULL(ai.aiopsAuthStatus, 'UNAUTH') AS aiopsAuthStatus,
               adim.*,
               aigm.aiopsItemGroup
        FROM aiops_snmp_instance asi
        LEFT JOIN aiops_instance ai ON asi.snmpId = ai.aiopsItemId
        <include refid="getJoinAiopskitDeviceSql">
            <property name="aiAlias" value="ai"/>
        </include>
        <include refid="getJoinAiopsItemGroupSql">
            <property name="aiAlias" value="ai"/>
        </include>
        WHERE asi.eid = #{eid}
        <if test="snmpType != null and snmpType != ''">
            AND asi.snmpType = #{snmpType}
        </if>
        <if test="snmpNameOrId != null and snmpNameOrId != ''">
            AND (asi.snmpName LIKE CONCAT('%', #{snmpNameOrId}, '%')
                 OR asi.snmpId LIKE CONCAT('%', #{snmpNameOrId}, '%'))
        </if>
        <if test="snmpPlacementPoint != null and snmpPlacementPoint != ''">
            AND asi.snmpPlacementPoint LIKE CONCAT('%', #{snmpPlacementPoint}, '%')
        </if>
        <if test="snmpIpAddress != null and snmpIpAddress != ''">
            AND asi.snmpIpAddress = #{snmpIpAddress}
        </if>
        <if test="aiopsAuthStatusList != null">
            <foreach collection="aiopsAuthStatusList" item="item"
                     open=" AND ai.aiopsAuthStatus IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        <!--考虑(日后场景)代理主机可能会有多台，因此在限制代理设备时，只能透过aiId去约束-->
        <if test="(agentDeviceNameOrPlacementPoint != null and agentDeviceNameOrPlacementPoint != '') or
                  (platformList != null and platformList.size() > 0)">
            AND EXISTS (
                SELECT 1
                FROM aiops_device ad
                LEFT JOIN aiops_device_type_mapping adtm ON ad.id = adtm.adId
                LEFT JOIN aiops_device_instance_mapping adim3 ON ad.id = adim3.adId
                WHERE ad.eid = #{eid} AND adim3.aiId = adim.aiId
                <if test="agentDeviceNameOrPlacementPoint != null and agentDeviceNameOrPlacementPoint != ''">
                    AND (adim.ad_deviceName LIKE CONCAT('%', #{agentDeviceNameOrPlacementPoint}, '%')
                    OR adim.ad_placementPoint LIKE CONCAT('%', #{agentDeviceNameOrPlacementPoint}, '%'))
                </if>
                <if test="platformList != null">
                    <foreach collection="platformList" item="item" open=" AND adim.ad_platform IN (" separator=", " close=")">
                        #{item}
                    </foreach>
                </if>
            )
        </if>
        ORDER BY CASE aiopsAuthStatus WHEN 'AUTHED' THEN 1
                 WHEN 'NONE' THEN 2
                 WHEN 'UNAUTH' THEN 3
                 ELSE 4 END, asi.snmpName
    </select>

    <insert id="batchInsertOrUpdateAiopsInstance" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO aiops_instance (id, eid, tmcdId, samcdId, aiopsItemType, aiopsItem, aiopsItemId, aiopsAuthStatus,
                                    execParamsModelCode, oriExecParamsContent)
        <foreach collection="aiCollection" item="item" open=" VALUES(" separator="), (" close=")">
            #{item.id}, #{item.eid}, #{item.tmcdId}, #{item.samcdId}, #{item.aiopsItemType}, #{item.aiopsItem},
            #{item.aiopsItemId}, #{item.aiopsAuthStatus}, #{item.execParamsModelCode}, #{item.oriExecParamsContent}
        </foreach>
        ON DUPLICATE KEY UPDATE tmcdId = VALUES(tmcdId), aiopsAuthStatus = VALUES(aiopsAuthStatus),
                                execParamsModelCode = VALUES(execParamsModelCode),
                                oriExecParamsContent = VALUES(oriExecParamsContent),
    </insert>

    <insert id="batchInsertAiopsInstance" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO aiops_instance (id, eid, tmcdId, samcdId, aiopsItemType, aiopsItem, aiopsItemId, aiopsAuthStatus,
                                    execParamsModelCode, oriExecParamsContent, independentAuth)
        <foreach collection="aiCollection" item="item" open=" VALUES(" separator="), (" close=")">
            #{item.id}, #{item.eid}, #{item.tmcdId}, #{item.samcdId}, #{item.aiopsItemType}, #{item.aiopsItem},
            #{item.aiopsItemId}, #{item.aiopsAuthStatus}, #{item.execParamsModelCode}, #{item.oriExecParamsContent},
            #{item.independentAuth}
        </foreach>
    </insert>

    <update id="batchUpdateAiopsInstance">
        UPDATE aiops_instance
        SET id = id
        <if test="aiCollection != null">
            <foreach collection="aiCollection" item="item"
                     open=", eid = CASE id WHEN " separator=" WHEN " close=" END">
                #{item.id} THEN #{item.eid}
            </foreach>
            <foreach collection="aiCollection" item="item"
                     open=", tmcdId = CASE id WHEN " separator=" WHEN " close=" END">
                #{item.id} THEN #{item.tmcdId}
            </foreach>
            <foreach collection="aiCollection" item="item"
                     open=", aiopsAuthStatus = CASE id WHEN " separator=" WHEN " close=" END">
                #{item.id} THEN #{item.aiopsAuthStatus}
            </foreach>
            <foreach collection="aiCollection" item="item"
                     open=", execParamsModelCode = CASE id WHEN " separator=" WHEN " close=" END">
                #{item.id} THEN #{item.execParamsModelCode}
            </foreach>
            <foreach collection="aiCollection" item="item"
                     open=", oriExecParamsContent = CASE id WHEN " separator=" WHEN " close=" END">
                #{item.id} THEN #{item.oriExecParamsContent}
            </foreach>
            <foreach collection="aiCollection" item="item"
                     open=", recoverableAuth = CASE id WHEN " separator=" WHEN " close=" END">
                #{item.id} THEN #{item.recoverableAuth}
            </foreach>
            <foreach collection="aiCollection" item="item"
                     open=", independentAuth = CASE id WHEN " separator=" WHEN " close=" END">
                #{item.id} THEN #{item.independentAuth}
            </foreach>
        </if>
        WHERE 1 != 1
        <if test="aiCollection != null">
            <foreach collection="aiCollection" item="item" open=" OR id IN (" separator=", " close=")">
                #{item.id}
            </foreach>
        </if>
    </update>

    <update id="batchUpdateAiopsInstanceOriExecParamsContent"
            parameterType="com.digiwin.escloud.aioitms.instance.model.AiopsInstance">
        UPDATE aiops_instance
        SET id = id
        <if test="aiCollection != null">
            <foreach collection="aiCollection" item="item"
                     open=", execParamsModelCode = CASE id WHEN " separator=" WHEN " close=" END">
                #{item.id} THEN #{item.execParamsModelCode}
            </foreach>
            <foreach collection="aiCollection" item="item"
                     open=", oriExecParamsContent = CASE id WHEN " separator=" WHEN " close=" END">
                #{item.id} THEN #{item.oriExecParamsContent}
            </foreach>
        </if>
        WHERE 1 != 1
        <if test="aiCollection != null">
            <foreach collection="aiCollection" item="item" open=" OR id IN (" separator=", " close=")">
                #{item.id}
            </foreach>
        </if>
    </update>

    <select id="selectAuthedCountByTmcdId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM aiops_instance
        WHERE tmcdId = #{tmcdId}
        <choose>
            <when test="isContainHoldAuth">
                AND aiopsAuthStatus = 'AUTHED'
            </when>
            <otherwise>
                AND aiopsAuthStatus = 'NONE'
            </otherwise>
        </choose>
    </select>

    <select id="selectAuthedCountByAiopsItemList" resultType="java.util.Map">
        SELECT tmcdId, COUNT(*) AS authedCount
        FROM aiops_instance ai
        WHERE ai.eid = #{eid} AND aiopsAuthStatus = 'AUTHED'
        <if test="aiopsItemList != null">
            AND ai.tmcdId IN (SELECT tmcdId FROM aiops_instance WHERE eid = #{eid} AND aiopsAuthStatus = 'AUTHED'
            <foreach collection="aiopsItemList" item="item" open=" AND aiopsItem IN(" separator=", " close=")">
                #{item}
            </foreach>
            )
        </if>
        GROUP BY tmcdId
    </select>

    <select id="selectStatisticsValidAiopsItemListByEid" resultMap="AiopsItemStatisticsMap">
        <!--查询分成3个部分，1:实例存在的统计；2:实例不存在的统计；3:未定义设备的统计-->
        SELECT aiopsItem, samcdId, SUM(total) AS total, hasDevices
        FROM (
            SELECT ai.aiopsItem, ai.samcdId, COUNT(IF(ai.aiopsAuthStatus != 'INVALID', TRUE, NULL)) AS total,
                   (CASE WHEN COUNT(ai.id) > 0 THEN 1 ELSE 0 END) AS hasDevices
            FROM aiops_instance ai
            WHERE ai.eid = #{eid} /*AND ai.aiopsAuthStatus != 'INVALID'*/
            GROUP BY ai.samcdId
            UNION ALL
            SELECT adtm.deviceType AS aiopsItem, 0 AS samcdId, COUNT(IF(ad.isDeleted = 0, TRUE, NULL)) AS total,
                   (CASE WHEN COUNT(ad.id) > 0 THEN 1 ELSE 0 END) AS hasDevices
            FROM aiops_device ad
            INNER JOIN aiops_device_type_mapping adtm ON ad.id = adtm.adId
            LEFT JOIN (
                SELECT adim2.id, adim2.adId
                FROM aiops_device_instance_mapping adim2
                INNER JOIN aiops_instance ai2 ON ai2.eid = #{eid} AND ai2.aiopsItemType = 'DEVICE'
                                                 AND adim2.aiId = ai2.id
            ) adim ON ad.id = adim.adId
            WHERE /*ad.isDeleted = 0 AND*/ ad.eid = #{eid} AND adim.id IS NULL
            GROUP BY adtm.deviceType
            UNION ALL
            SELECT 'UNDEFINED' AS aiopsItem, 0 AS samcdId, COUNT(IF(ad.isDeleted = 0, TRUE, NULL)) AS total,
                   (CASE WHEN COUNT(ad.id) > 0 THEN 1 ELSE 0 END) AS hasDevices
            FROM aiops_device ad
            LEFT JOIN aiops_device_type_mapping adtm ON ad.id = adtm.adId
            WHERE /*ad.isDeleted = 0 AND*/ ad.eid = #{eid} AND adtm.id IS NULL
            GROUP BY adtm.deviceType
        ) main
        where main.aiopsItem != 'TMP_RH' /*舊的查找 logic 不適用在溫濕度*/
        GROUP BY aiopsItem
        union all
        /* 計算溫濕度的數量*/
        SELECT ai.aiopsItem, ai.samcdId, COUNT(IF(ai.aiopsAuthStatus != 'INVALID', TRUE, NULL)) AS total,
               (CASE WHEN COUNT(ai.id) > 0 THEN 1 ELSE 0 END) AS hasDevices
        FROM aiops_instance ai
        WHERE ai.eid = #{eid}
          and ai.aiopsItemType = 'TMP_RH'
          AND ai.aiopsAuthStatus in ('AUTHED', 'UNAUTH', 'INVALID')
        GROUP BY ai.samcdId
    </select>
    <select id="selectDeviceDashboardDetail"  resultType="com.digiwin.escloud.aioitms.device.model.DeviceDashboardDetail">
        select * from (
                          SELECT ai.aiopsItem, ai.samcdId, ai.aiopsItemId
                          FROM aiops_instance ai
                          WHERE ai.eid = #{eid} AND ai.aiopsAuthStatus != 'INVALID'
                          UNION ALL
                          SELECT adtm.deviceType AS aiopsItem, 0 AS samcdId, ad.deviceId aiopsItemId
                          FROM aiops_device ad
                                   INNER JOIN aiops_device_type_mapping adtm ON ad.id = adtm.adId
                                   LEFT JOIN (
                              SELECT adim2.id, adim2.adId
                              FROM aiops_device_instance_mapping adim2
                                       INNER JOIN aiops_instance ai2 ON ai2.eid = #{eid} AND ai2.aiopsItemType = 'DEVICE'
                                  AND adim2.aiId = ai2.id
                          ) adim ON ad.id = adim.adId
                          WHERE ad.isDeleted = 0 AND ad.eid = #{eid} AND adim.id IS NULL

                          UNION ALL
                          SELECT 'UNDEFINED' AS aiopsItem, 0 AS samcdId, ad.deviceId aiopsItemId
                          FROM aiops_device ad
                                   LEFT JOIN aiops_device_type_mapping adtm ON ad.id = adtm.adId
                          WHERE ad.isDeleted = 0 AND ad.eid = #{eid} AND adtm.id IS NULL
                          UNION ALL
                          SELECT ai.aiopsItem, ai.samcdId, ai.aiopsItemId
                          FROM aiops_instance ai
                          WHERE ai.eid = #{eid}
                            and ai.aiopsItemType = 'TMP_RH'
                            AND ai.aiopsAuthStatus in ('AUTHED', 'UNAUTH')
                      ) as aa
    </select>
    <select id="selectBatchAuthList" resultType="java.util.Map">
        SELECT main.*, ai2.name AS aiopsItemName,
               ai2.name_CN AS aiopsItemName_CN, ai2.name_TW AS aiopsItemName_TW
        FROM (
            SELECT ad.deviceName, ad.ipAddress, ai.aiopsItem, IFNULL(ad.placementPoint, '') AS placementPoint,
                   ad.lastCheckInTime, IFNULL(ad.aiopskitVersion, '') AS aiopskitVersion,
                   ad.lastCheckInTime >= DATE_SUB(now(), INTERVAL 3 DAY) AS isOnline,
                   ai.aiopsItemType, ai.aiopsItemId, ad.deviceId
            FROM aiops_instance ai
            INNER JOIN aiops_device ad ON ai.aiopsItemType = 'DEVICE' AND ai.aiopsItemId = ad.deviceId
            <if test="isOnline != null">
                <choose>
                    <when test="isOnline">
                        AND ad.lastCheckInTime >= DATE_SUB(now(), INTERVAL 3 DAY)
                    </when>
                    <otherwise>
                        AND ad.lastCheckInTime <![CDATA[<]]> DATE_SUB(now(), INTERVAL 3 DAY)
                    </otherwise>
                </choose>
            </if>
            <if test="placementPoint != null and placementPoint != ''">
                AND ad.placementPoint LIKE CONCAT('%', #{placementPoint}, '%')
            </if>
            WHERE ai.eid = #{eid} AND aiopsAuthStatus = 'UNAUTH'
            <if test="aiopsItemList != null">
                <foreach collection="aiopsItemList" item="item" open="AND ai.aiopsItem IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            UNION
            SELECT IFNULL(asi.snmpName, '') AS deviceName, IFNULL(asi.snmpIpAddress, '') AS ipAddress,
                   ai.aiopsItem, IFNULL(asi.snmpPlacementPoint, '') AS placementPoint,
                   ad.lastCheckInTime, IFNULL(ad.aiopskitVersion, '') AS aiopskitVersion,
                   ad.lastCheckInTime >= DATE_SUB(now(), INTERVAL 3 DAY) AS isOnline,
                   ai.aiopsItemType, ai.aiopsItemId, ad.deviceId
            FROM aiops_instance ai
            INNER JOIN aiops_snmp_instance asi ON ai.aiopsItemType = 'SNMP' AND ai.aiopsItemId = asi.snmpId
            <if test="placementPoint != null and placementPoint != ''">
                AND asi.snmpPlacementPoint LIKE CONCAT('%', #{placementPoint}, '%')
            </if>
            LEFT JOIN aiops_device_instance_mapping adim ON ai.id = adim.aiId
            LEFT JOIN aiops_device ad ON adim.adId = ad.id
            WHERE ai.eid = #{eid} AND aiopsAuthStatus = 'UNAUTH'
            <if test="aiopsItemList != null">
                <foreach collection="aiopsItemList" item="item" open="AND ai.aiopsItem IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="isOnline != null">
                <choose>
                    <when test="isOnline">
                        AND ad.lastCheckInTime >= DATE_SUB(now(), INTERVAL 3 DAY)
                    </when>
                    <otherwise>
                        AND ad.lastCheckInTime <![CDATA[<]]> DATE_SUB(now(), INTERVAL 3 DAY)
                    </otherwise>
                </choose>
            </if>
        ) main
        LEFT JOIN aiops_item ai2 ON main.aiopsItem = ai2.code
        ORDER BY deviceName
    </select>

    <select id="selectAiopsSNMPInstanceBySNMPId" resultType="com.digiwin.escloud.aioitms.instance.model.AiopsSNMPInstance">
        SELECT *
        FROM aiops_snmp_instance
        WHERE snmpId = #{snmpId}
    </select>

    <select id="selectSNMPIdByAsiId" resultType="java.lang.String">
        SELECT snmpId
        FROM aiops_snmp_instance
        WHERE id = #{asiId}
    </select>

    <select id="selectSNMPInstanceListByEid" resultType="com.digiwin.escloud.aioitms.instance.model.AiopsSNMPInstance">
        SELECT asi.*, ai.aiopsItemType, ai.aiopsItem, IFNULL(ai.aiopsAuthStatus, 'UNAUTH') AS aiopsAuthStatus,
               ait.name AS aiopsItemTypeName, ait.name_CN AS aiopsItemTypeName_CN, ait.name_TW AS aiopsItemTypeName_TW,
               ai2.name AS aiopsItemName, ai2.name_CN AS aiopsItemName_CN, ai2.name_TW AS aiopsItemName_TW
        FROM aiops_snmp_instance asi
        LEFT JOIN aiops_instance ai ON asi.snmpId = ai.aiopsItemId
        LEFT JOIN aiops_item_type ait ON ai.aiopsItemType = ait.code
        LEFT JOIN aiops_item ai2 ON ai.aiopsItem = ai2.code
        WHERE asi.eid = #{eid}
        <if test="snmpType != null and snmpType != ''">
            AND asi.snmpType = #{snmpType}
        </if>
        ORDER BY asi.snmpName
    </select>

    <select id="selectAiopsHTTPInstanceByApiCollectId"
            resultType="com.digiwin.escloud.aioitms.instance.model.AiopsHttpInstance">
        SELECT *
        FROM aiops_http_instance
        WHERE apiCollectId = #{apiCollectId}
    </select>

    <insert id="batchInsertOrUpdateAiopsSNMPInstance" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO aiops_snmp_instance (id, eid, snmpType, snmpId, snmpName, snmpIpAddress, snmpPort,
                                         snmpPlacementPoint, snmpRemark, snmpCustomerNotes, snmpVersion, snmpCommunity, snmpContextName,
                                         snmpSecurityLevel, snmpUserName, snmpAuthProtocol, snmpAuthPassword,
                                         snmpPrivProtocol, snmpPrivPassword)
        <foreach collection="aiopsSNMPInstanceList" item="item" open=" VALUES(" separator="), (" close=")">
            #{item.id}, #{item.eid}, #{item.snmpType}, #{item.snmpId}, #{item.snmpName}, #{item.snmpIpAddress},
            #{item.snmpPort}, #{item.snmpPlacementPoint}, #{item.snmpRemark}, #{item.snmpCustomerNotes}, #{item.snmpVersion},
            #{item.snmpCommunity},#{item.snmpContextName}, #{item.snmpSecurityLevel}, #{item.snmpUserName},
            #{item.snmpAuthProtocol},#{item.snmpAuthPassword}, #{item.snmpPrivProtocol}, #{item.snmpPrivPassword}
        </foreach>
        ON DUPLICATE KEY UPDATE eid = VALUES(eid), snmpType = VALUES(snmpType), snmpId = VALUES(snmpId),
                                snmpName = VALUES(snmpName), snmpIpAddress = VALUES(snmpIpAddress),
                                snmpPort = VALUES(snmpPort),
                                -- todo 由于地端没有添加对应字段所以添加非空判断
                                snmpPlacementPoint = IFNULL(VALUES(snmpPlacementPoint), snmpPlacementPoint),
                                snmpRemark = IFNULL(VALUES(snmpRemark), snmpRemark),
                                snmpCustomerNotes = IFNULL(VALUES(snmpCustomerNotes), snmpCustomerNotes),
                                snmpVersion = VALUES(snmpVersion), snmpCommunity = VALUES(snmpCommunity),
                                snmpContextName = VALUES(snmpContextName),
                                snmpSecurityLevel = VALUES(snmpSecurityLevel),
                                snmpUserName = VALUES(snmpUserName),
                                snmpAuthProtocol = VALUES(snmpAuthProtocol),
                                snmpAuthPassword = VALUES(snmpAuthPassword),
                                snmpPrivProtocol = VALUES(snmpPrivProtocol),
                                snmpPrivPassword = VALUES(snmpPrivPassword)
    </insert>

    <select id="selectExistSNMPInstances" resultType="com.digiwin.escloud.aioitms.instance.model.AiopsSNMPInstance">
        select a.snmpIpAddress,a.snmpPort,a.snmpType
        from aiops_snmp_instance a
        where 1=1
        <if test="aiopsSNMPInstanceList != null">

            <foreach collection="aiopsSNMPInstanceList" item="item" open=" AND ( " separator=") or (" close=")">
                  a.snmpIpAddress = #{item.snmpIpAddress} and a.snmpPort = #{item.snmpPort} and a.snmpType = #{item.snmpType} and a.eid = #{item.eid}
            </foreach>
        </if>
    </select>

    <select id="selectHttpInstanceListByEid" resultType="com.digiwin.escloud.aioitms.instance.model.AiopsHttpInstance">
        SELECT ahi.*, ai.aiopsItemType, ai.aiopsItem, IFNULL(ai.aiopsAuthStatus, 'UNAUTH') AS aiopsAuthStatus,
               ait.name AS aiopsItemTypeName, ait.name_CN AS aiopsItemTypeName_CN, ait.name_TW AS aiopsItemTypeName_TW,
               ai2.name AS aiopsItemName, ai2.name_CN AS aiopsItemName_CN, ai2.name_TW AS aiopsItemName_TW
        FROM aiops_http_instance ahi
        LEFT JOIN aiops_instance ai ON ahi.apiCollectId = ai.aiopsItemId
        LEFT JOIN aiops_item_type ait ON ai.aiopsItemType = ait.code
        LEFT JOIN aiops_item ai2 ON ai.aiopsItem = ai2.code
        WHERE ahi.eid = #{eid}
        <if test="apiCollectType != null and apiCollectType != ''">
            AND ahi.apiCollectType = #{apiCollectType}
        </if>
        ORDER BY ahi.apiDeviceName
    </select>

    <select id="selectHttpInstanceList" resultType="com.digiwin.escloud.aioitms.instance.model.AiopsHttpInstance">
        SELECT ahi.*, ai.aiopsItemType, ai.aiopsItem, IFNULL(ai.aiopsAuthStatus, 'UNAUTH') AS aiopsAuthStatus,
            ait.name AS aiopsItemTypeName, ait.name_CN AS aiopsItemTypeName_CN, ait.name_TW AS aiopsItemTypeName_TW,
            ai2.name AS aiopsItemName, ai2.name_CN AS aiopsItemName_CN, ai2.name_TW AS aiopsItemName_TW
        FROM aiops_http_instance ahi
        LEFT JOIN aiops_instance ai ON ahi.apiCollectId = ai.aiopsItemId
        LEFT JOIN aiops_item_type ait ON ai.aiopsItemType = ait.code
        LEFT JOIN aiops_item ai2 ON ai.aiopsItem = ai2.code
        WHERE ahi.eid = #{eid}
        <if test="apiCollectType != null and apiCollectType != ''">
            AND ahi.apiCollectType = #{apiCollectType}
        </if>
        <if test="aiopsAuthStatusList != null">
            AND ai.aiopsAuthStatus IN
            <foreach collection="aiopsAuthStatusList" item="item" open="(" separator=", " close=")">
                 #{item}
            </foreach>
        </if>
        ORDER BY FIELD(aiopsAuthStatus, 'AUTHED', 'NONE', 'UNAUTH'), ahi.apiDeviceName
    </select>

    <insert id="batchInsertOrUpdateAiopsHttpInstance" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO aiops_http_instance(id, eid, apiCollectType, apiCollectId, apiDeviceName, apiDevicePlace,
        apiRemark, apiCustomerNotes, ip, apiAddress, apiBasicLoginId, apiBasicLoginPwd, apiBasicAuthCode)
        <foreach collection="aiopsHttpInstanceList" item="item" open=" VALUES(" separator="), (" close=")">
            #{item.id}, #{item.eid}, #{item.apiCollectType}, #{item.apiCollectId},
            #{item.apiDeviceName}, #{item.apiDevicePlace}, #{item.apiRemark}, #{item.apiCustomerNotes},
            #{item.ip}, #{item.apiAddress}, #{item.apiBasicLoginId}, #{item.apiBasicLoginPwd}, #{item.apiBasicAuthCode}
        </foreach>
        ON DUPLICATE KEY UPDATE eid = VALUES(eid), apiCollectType = VALUES(apiCollectType),
                                apiCollectId = VALUES(apiCollectId), apiDeviceName = VALUES(apiDeviceName),
                                apiDevicePlace = IFNULL(VALUES(apiDevicePlace), apiDevicePlace),
                                apiRemark = IFNULL(VALUES(apiRemark), apiRemark),
                                apiCustomerNotes = IFNULL(VALUES(apiCustomerNotes), apiCustomerNotes), ip = VALUES(ip),
                                apiAddress = VALUES(apiAddress), apiBasicLoginId = VALUES(apiBasicLoginId),
                                apiBasicLoginPwd = VALUES(apiBasicLoginPwd), apiBasicAuthCode = VALUES(apiBasicAuthCode)
    </insert>

    <select id="selectExistHttpInstances" resultType="com.digiwin.escloud.aioitms.instance.model.AiopsHttpInstance">
        select a.*
        from aiops_http_instance a
        where 1=1
        <if test="aiopsHttpInstanceList != null">

            <foreach collection="aiopsHttpInstanceList" item="item" open=" AND ( " separator=") or (" close=")">
                a.eid = #{item.eid} and a.apiCollectType = #{item.apiCollectType} and a.apiAddress = #{item.apiAddress}
            </foreach>
        </if>
    </select>

    <select id="selectApiCollectIdByAhiId" resultType="java.lang.String">
        SELECT apiCollectId
        FROM aiops_http_instance
        WHERE id = #{ahiId}
    </select>

    <select id="selectAiopsInstanceByAiopsItemIdList" resultType="com.digiwin.escloud.aioitms.instance.model.AiopsInstance">
        SELECT *
        FROM aiops_instance
        WHERE 1 = 1
        <if test="aiopsItemIdList != null">
            <foreach collection="aiopsItemIdList" item="item" open=" AND aiopsItemId IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectAiIdByAiopsItemId" resultType="java.lang.Long">
        SELECT id
        FROM aiops_instance
        WHERE aiopsItemId = #{aiopsItemId}
    </select>

    <select id="selectAiopsItemIdByAiId" resultType="java.lang.String">
        SELECT aiopsItemId
        FROM aiops_instance
        WHERE id = #{aiId}
    </select>

    <update id="updateAiopsInstanceAuthStatusByContractExpire">
        UPDATE aiops_instance
        SET aiopsAuthStatus = 'UNAUTH', recoverableAuth = 1
        WHERE aiopsAuthStatus = 'AUTHED' AND (1 != 1
        <if test="tmcdIdList != null">
            <foreach collection="tmcdIdList" item="item" open=" OR tmcdId IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        )
    </update>

    <select id="selectAiEidListByTmcdIdList" resultType="java.lang.Long">
        SELECT eid
        FROM aiops_instance
        WHERE 1 != 1
        <if test="tmcdIdList != null">
            <foreach collection="tmcdIdList" item="item" open=" OR tmcdId IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectAuthStatusMapByAiopsItemIdList" resultType="java.util.Map">
        SELECT aiopsItemId, aiopsAuthStatus
        FROM aiops_instance
        WHERE 1 != 1
        <if test="aiopsItemIdList != null">
            <foreach collection="aiopsItemIdList" item="item" open=" OR aiopsItemId IN(" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectSNMPInstanceListByDeviceId" resultMap="snmpInfoMap">
        <!--排序规则：考虑到通常地端主要是进行测试连接，因此把未授权放在上面(方便快速验证，然后开启授权)-->
        SELECT asi.*,
               ai.aiopsItemType, ai.aiopsItem,
               IFNULL(ai.aiopsAuthStatus, 'UNAUTH') AS aiopsAuthStatus
        FROM aiops_device_instance_mapping adim
        INNER JOIN aiops_instance ai ON ai.aiopsItemType = 'SNMP' AND adim.aiId = ai.id
        INNER JOIN aiops_snmp_instance asi ON ai.aiopsItemId = asi.snmpId
        WHERE adim.deviceId = #{deviceId}
        ORDER BY CASE IFNULL(ai.aiopsAuthStatus, 'UNAUTH') WHEN 'UNAUTH' THEN 1
                 WHEN 'NONE' THEN 2
                 WHEN 'AUTHED' THEN 3
                 ELSE 4 END, asi.snmpName
    </select>

    <select id="selectAuthedCountMapByTmcdIdList" resultType="java.util.Map">
        SELECT tmcdId, COUNT(*) AS authedCount
        FROM aiops_instance ai
        <where>
            <choose>
                <when test="isContainHoldAuth">
                    AND aiopsAuthStatus = 'AUTHED'
                </when>
                <otherwise>
                    AND aiopsAuthStatus = 'NONE'
                </otherwise>
            </choose>
            <if test="eid != null and eid > 0">
                AND ai.eid = #{eid}
            </if>
            <if test="tmcdIdList != null">
                <foreach collection="tmcdIdList" item="item" open=" AND tmcdId IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY tmcdId
    </select>

    <select id="selectDeviceIdListByAicList" resultType="java.lang.String">
        SELECT DISTINCT adim.deviceId
        FROM aiops_instance ai
        INNER JOIN aiops_device_instance_mapping adim ON ai.id = adim.aiId
        WHERE 1 != 1
        <if test="aicList != null">
            <foreach collection="aicList" item="item"
                     open=" OR ai.aiopsItemId IN(" separator=", " close=")">
                #{item.aiopsItemId}
            </foreach>
        </if>
    </select>

    <update id="invalidDeviceAiopsInstanceAuthStatus">
        UPDATE aiops_instance
        SET aiopsAuthStatus = 'INVALID'
        WHERE aiopsItemType = 'DEVICE' AND aiopsItemId = #{deviceId}
    </update>

    <select id="selectAiopsInstanceByAiIdList" resultType="com.digiwin.escloud.aioitms.instance.model.AiopsInstance">
        SELECT *
        FROM aiops_instance
        WHERE 1 != 1
        <if test="aiIdList != null">
            <foreach collection="aiIdList" item="item" open=" OR id IN(" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectSNMPInstanceBySNMPId" resultMap="snmpInstanceMap">
        SELECT *
        FROM aiops_snmp_instance
        WHERE snmpId = #{snmpId}
    </select>

    <select id="selectAiopsBackupSoftwareInstanceByBackupSoftwareId"
            resultType="com.digiwin.escloud.aioitms.instance.model.AiopsBackupSoftwareInstance">
        SELECT *
        FROM aiops_backup_software_instance
        WHERE backupSoftwareId = #{backupSoftwareId}
    </select>

    <select id="selectAiopsBackupSoftwareInstanceByAbsiId" resultType="java.lang.String">
        SELECT backupSoftwareId
        FROM aiops_backup_software_instance
        WHERE id = #{absiId}
    </select>
    
    <insert id="batchInsertOrUpdateAiopsBackupSoftwareInstance">
        INSERT INTO aiops_backup_software_instance (id, eid, deviceId, backupSoftwareType,
                                                    backupSoftwareDisplayName, backupSoftwareId)
        <foreach collection="aiopsBackupSoftwareInstanceList" item="item" open=" VALUES(" separator="), (" close=")">
            #{item.id}, #{item.eid}, #{item.deviceId}, #{item.backupSoftwareType},
            #{item.backupSoftwareDisplayName}, #{item.backupSoftwareId}
        </foreach>
        ON DUPLICATE KEY UPDATE eid = VALUES(eid), deviceId = VALUES(deviceId),
                                backupSoftwareType = VALUES(backupSoftwareType),
                                backupSoftwareDisplayName = VALUES(backupSoftwareDisplayName),
                                backupSoftwareId = VALUES(backupSoftwareId)
    </insert>

    <select id="selectAiopsInstanceByMap" resultType="com.digiwin.escloud.aioitms.instance.model.AiopsInstance">
        SELECT ai.*
        FROM aiops_instance ai
        WHERE 1 = 1
        <if test="eid != null and eid != 0">
            AND ai.eid = #{eid}
        </if>
        <if test="aiopsItem != null and aiopsItem != ''">
            AND ai.aiopsItem = #{aiopsItem}
        </if>
        <if test="aiopsItemId != null and aiopsItemId != ''">
            AND ai.aiopsItemId = #{aiopsItemId}
        </if>
        <if test="onlyEmptyExecParamsContent != null and onlyEmptyExecParamsContent">
            AND IFNULL(ai.execParamsModelCode, '') != '' AND IFNULL(ai.oriExecParamsContent, '') = ''
        </if>
        <if test="aiopsItemList != null">
            <foreach collection="aiopsItemList" item="item" open=" AND ai.aiopsItem IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getInstances" resultType="com.digiwin.escloud.aioitms.instance.model.AiopsInstance">
        SELECT t.*,item.name aiopsItemName,item.name_CN aiopsItemName_CN,item.name_TW aiopsItemName_TW FROM (
        SELECT id,eid,aiopsItemType,aiopsItem,aiopsItemId,aiopsAuthStatus,
        <include refid="com.digiwin.escloud.aioitms.instance.dao.InstanceMapper.getAiopsInstanceWithNoIpDisplayNameSql">
            <property name="aiAlias" value="ai"/>
        </include> AS aiopsInstanceName
        FROM aiops_instance ai
        WHERE 1 = 1
#             正式环境 查询性能不满足，优化查询过滤条件至子查询
        <if test="eid != null and eid != 0">
            AND ai.eid = #{eid}
        </if>
        and aiopsAuthStatus in
        <foreach collection="aiopsAuthStatusList" item="aiopsAuthStatus" open="(" separator="," close=")">
            #{aiopsAuthStatus}
        </foreach>
        ) t
        left join aiops_item item on t.aiopsItem=item.code
        <where>
            ifnull(aiopsInstanceName,'')!=''
            <if test="content != null and content != ''">
                and (aiopsItemId like CONCAT('%',#{content},'%') or aiopsInstanceName like CONCAT('%',#{content},'%'))
            </if>
            <if test="aiopsItem != null and aiopsItem != ''">
                and aiopsItem =#{aiopsItem}
            </if>
            <if test="showInApp!=null">
                <if test="showInApp==false">
                    AND showInApp=0
                </if>
                <if test="showInApp==true">
                    AND showInApp=1
                </if>
            </if>
        </where>
        order by aiopsInstanceName
    </select>

    <select id="getInstanceByInstanceIds" resultType="com.digiwin.escloud.aioitms.instance.model.AiopsInstance">
        SELECT * FROM (
        SELECT id,eid,aiopsItemType,aiopsItem,aiopsItemId,
        <include refid="com.digiwin.escloud.aioitms.instance.dao.InstanceMapper.getAiopsInstanceWithIpDisplayNameSql">
            <property name="aiAlias" value="ai"/>
        </include> AS aiopsInstanceName
        FROM aiops_instance ai) t
        <where>
            ifnull(aiopsInstanceName,'')!='' and aiopsItemId in
            <foreach collection="instanceIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test="aiopsItem != null and aiopsItem != ''">
                and aiopsItem=#{aiopsItem}
            </if>
        </where>
    </select>

    <select id="getAiopsItemByTenantSid" resultType="com.digiwin.escloud.aioitms.instance.model.AiopsItem">
        SELECT ai.*,
        CASE
            WHEN ai2.aiopsItem IN ('iLO', 'iDRAC', 'XCC', 'iMM') THEN COUNT(ai2.aiopsItem)
            ELSE tmcd.usedCount
        END AS usedCount, tmcd.availableCount
        FROM tenant_module_contract tmc
        LEFT JOIN supplier_aiops_module_class samc ON tmc.moduleId = samc.samId
        LEFT JOIN supplier_aiops_module_class_detail samcd ON  samcd.samcId = samc.id
        LEFT JOIN tenant_module_contract_detail tmcd ON samc.id = tmcd.samcId AND tmc.id = tmcd.tmcId
        LEFT JOIN aiops_item ai ON ai.code = samcd.aiopsItem
        LEFT JOIN aiops_instance ai2 ON ai2.aiopsItem = ai.code AND ai2.eid = tmc.eid AND ai2.aiopsAuthStatus in ('NONE','UNAUTH','AUTHED')
        WHERE tmc.sid=#{sid} AND tmc.eid=#{tenantSid} AND TIMESTAMPDIFF(DAY, NOW(), endDate)>=0 and availableCount>0
        <if test="showInApp!=null">
            <if test="showInApp==false">
                AND showInApp=0
            </if>
            <if test="showInApp==true">
                AND showInApp=1
            </if>
        </if>
        GROUP BY ai2.aiopsItem
        order by ai.id
    </select>

    <select id="getAiopsItem" resultType="com.digiwin.escloud.aioitms.instance.model.AiopsItem">
        SELECT ai.*
        FROM aiops_item ai
    </select>

    <select id="selectAddAdcdInfo" resultType="java.util.Map">
        SELECT adim.deviceId AS sourceDeviceId, adim.aiId, adim.id AS adimId,
               ai.samcdId, ai.eid, ai.aiopsItem, ai.aiopsItemId,
               ai.execParamsModelCode, ai.oriExecParamsContent
        FROM aiops_device_instance_mapping adim
        INNER JOIN aiops_device ad ON adim.adId = ad.id
        INNER JOIN aiops_instance ai ON adim.aiId = ai.id
        <where>
            <if test="targetEidList != null">
                <foreach collection="targetEidList" item="item"
                         open=" AND ai.eid IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="targetDeviceIdList != null">
                <foreach collection="targetDeviceIdList" item="item"
                         open=" AND adim.deviceId IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="targetAiIdList != null">
                <foreach collection="targetAiIdList" item="item"
                         open=" AND ai.id IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="targetAiopsItemIdList != null">
                <foreach collection="targetAiopsItemIdList" item="item"
                         open=" AND ai.aiopsItemId IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="targetAiopsItemList != null">
                <foreach collection="targetAiopsItemList" item="item"
                         open=" AND ai.aiopsItem IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="accIdNotInAdcd != null and accIdNotInAdcd and accId != null and accId > 0">
                    AND NOT EXISTS(SELECT 1 FROM aiops_device_collect_detail adcd
                                   LEFT JOIN aiops_collect_config acc ON adcd.accId = acc.id
                                   WHERE adcd.adimId = adim.id AND adcd.aiId = adim.aiId
                                        AND IFNULL(acc.sourceAccId, acc.id) = #{accId})
            </if>
            <if test="aiopsAuthStatusList != null">
                <foreach collection="aiopsAuthStatusList" item="item"
                         open=" AND ai.aiopsAuthStatus IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="hasAddedAiopsInstance != null">
                AND adim.hasAddedAiopsInstance = #{hasAddedAiopsInstance}
            </if>
            <if test="targetPlatformList != null">
                <foreach collection="targetPlatformList" item="item"
                         open=" AND ad.platform IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="targetDeviceTypeList != null">
                <foreach collection="targetDeviceTypeList" item="item"
                         open=" AND EXISTS(SELECT 1 FROM aiops_device_type_mapping adtm
                                           WHERE adtm.adId = ad.id AND adtm.deviceType IN(" separator=", " close="))">
                    #{item}
                </foreach>
            </if>
            <if test="targetProductAppCodeList != null">
                <foreach collection="targetProductAppCodeList" item="item"
                         open=" AND EXISTS (SELECT 1 FROM aiops_device_product_mapping adpm
                                            INNER JOIN aiops_product_app apa ON adpm.apaId = apa.id
                                            WHERE adpm.appId = ai.aiopsItemId AND ai.aiopsItemType = 'PRODUCT_APP'
                                                AND apa.modelCode IN("
                         separator=", " close="))">
                    #{item}
                </foreach>
            </if>
            <if test="excludeEidList != null">
                <foreach collection="excludeEidList" item="item"
                         open=" AND ai.eid NOT IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="excludeDeviceIdList != null">
                <foreach collection="excludeDeviceIdList" item="item"
                         open=" AND adim.deviceId NOT IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="excludeAiIdList != null">
                <foreach collection="excludeAiIdList" item="item"
                         open=" AND ai.id NOT IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="excludeAiopsItemIdList != null">
                <foreach collection="excludeAiopsItemIdList" item="item"
                         open=" AND ai.aiopsItemId NOT IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectAiopsItemIdByRelate" resultType="java.lang.String">
        SELECT ${aiopsItemIdColumnName}
        FROM ${truestInstanceTableName}
        WHERE 1 != 1
        <if test="businessPKValueMap != null">
            <foreach collection="businessPKValueMap.entrySet()" index="key" item="value"
                     open=" OR (" separator=" AND " close=")">
              <choose>
                  <when test="value != null">
                      ${key} = #{value}
                  </when>
                  <otherwise>
                      1 = 1
                  </otherwise>
              </choose>
            </foreach>
        </if>
        LIMIT 1
    </select>

    <select id="selectAiopsItemIdListByMap" resultType="java.lang.String">
        SELECT aiopsItemId
        FROM aiops_instance ai
        <if test="deviceId != null and deviceId != ''">
            INNER JOIN aiops_device_instance_mapping adim ON ai.id = adim.aiId AND adim.deviceId = #{deviceId}
        </if>
        WHERE 1 = 1
        <if test="aiopsItem != null and aiopsItem != ''">
            AND ai.aiopsItem = #{aiopsItem}
        </if>
    </select>

    <select id="selectExistEDRInstances" resultType="com.digiwin.escloud.aioitms.model.instance.AiopsEDRInstance">
        SELECT *
        FROM aiops_edr_instance
        WHERE 1 != 1
        <if test="edrIdList != null">
            <foreach collection="edrIdList" item="item" open=" OR edrId IN ( " separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <insert id="batchInsertAiopsEDRInstance">
        INSERT INTO aiops_edr_instance (id, edrServerId, edrOrgId, edrOrgName, edrDeviceId, edrId,
                                        edrDeviceName, edrDeviceIpAddress)
        <foreach collection="aeiList" item="item" open=" VALUES( " separator="), (" close=")">
            #{item.id}, #{item.edrServerId}, #{item.edrOrgId}, #{item.edrOrgName},
            #{item.edrDeviceId}, #{item.edrId}, #{item.edrDeviceName}, #{item.edrDeviceIpAddress}
        </foreach>
    </insert>

    <select id="selectExistSmartMeterInstances"
            resultType="com.digiwin.escloud.aioitms.model.instance.AiopsSmartMeterInstance">
        SELECT *
        FROM aiops_smart_meter_instance
        WHERE 1 != 1
        <if test="smartMeterIdList != null">
            <foreach collection="smartMeterIdList" item="item" open=" OR smartMeterId IN ( " separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <update id="batchUpdateAiopsSmartMeterInstance">
        UPDATE aiops_smart_meter_instance
        SET lastCheckInTime = now()
        <if test="asmiList != null">
            <foreach collection="asmiList" item="item"
                     open=", smartMeterDeviceName = CASE id WHEN " separator=" WHEN " close=" END ">
                #{item.id} THEN
                <choose>
                    <when test="item.smartMeterDeviceName == null or item.smartMeterDeviceName == ''">
                        smartMeterDeviceName
                    </when>
                    <otherwise>
                        #{item.smartMeterDeviceName}
                    </otherwise>
                </choose>
            </foreach>
        </if>
        WHERE 1 != 1
        <if test="asmiList != null">
            <foreach collection="asmiList" item="item" open=" OR id IN(" separator=", " close=")">
                #{item.id}
            </foreach>
        </if>
    </update>

    <insert id="batchInsertAiopsSmartMeterInstance">
        INSERT INTO aiops_smart_meter_instance(id, eid, smartMeterDeviceId, smartMeterDeviceName, smartMeterId)
        <foreach collection="asmiList" item="item" open=" VALUES( " separator="), (" close=")">
            #{item.id}, #{item.eid}, #{item.smartMeterDeviceId}, #{item.smartMeterDeviceName}, #{item.smartMeterId}
        </foreach>
    </insert>

    <select id="selectExistEaiCloudInstance"
            resultType="com.digiwin.escloud.aioitms.instance.model.AiopsEaiCloudInstance">
        SELECT *
        FROM aiops_eai_cloud_instance
        WHERE 1 != 1
        <if test="eaiCloudIdList != null">
            <foreach collection="eaiCloudIdList" item="item" open=" OR eaiCloudId IN ( " separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <insert id="batchInsertAiopsEaiCloudInstance">
        INSERT INTO aiops_eai_cloud_instance(id, eaiCloudId, eaiType, serviceCode)
        <foreach collection="aeciList" item="item" open=" VALUES( " separator="), (" close=")">
            #{item.id}, #{item.eaiCloudId}, #{item.eaiType}, #{item.serviceCode}
        </foreach>
    </insert>

    <update id="updateAiopsEaiCloudInstance">
        UPDATE aiops_eai_cloud_instance aeci
        INNER JOIN aiops_instance ai ON ai.aiopsItemType = #{aiopsItemType} AND aeci.eaiCloudId = ai.aiopsItemId
        INNER JOIN aiops_device_collect_detail adcd ON ai.id = adcd.aiId
        INNER JOIN aiops_device_collect_warning adcw ON adcd.id = adcw.adcdId
        SET adcw.isWarningEnable = #{isWarningEnable}
        WHERE aeci.serviceCode = #{serviceCode} AND aeci.eaiType = #{eaiType}
    </update>

    <select id="selectAiopsEaiCloudIsWarningEnable" resultType="java.util.Map">
        SELECT MAX(IFNULL(adcw.isWarningEnable, 0) = 1) AS isWarningEnable, ai.id AS aiId
        FROM aiops_eai_cloud_instance aeci
        INNER JOIN aiops_instance ai ON ai.aiopsItemType = #{aiopsItemType} AND aeci.eaiCloudId = ai.aiopsItemId
        INNER JOIN aiops_device_collect_detail adcd ON ai.id = adcd.aiId
        INNER JOIN aiops_device_collect_warning adcw ON adcd.id = adcw.adcdId
        WHERE aeci.serviceCode = #{serviceCode}
        GROUP BY ai.id
    </select>

    <select id="selectAiopsEaiCloudInstanceDetail"
            resultType="com.digiwin.escloud.aioitms.instance.model.AiopsEaiCloudInstanceDetail">
        SELECT ai.eid, ai.id as aiId, ai.aiopsItemId,
               aeci.serviceCode as serviceCode, adcd.accId AS collectConfigId, adcd.id AS deviceCollectDetailId
        FROM aiops_eai_cloud_instance aeci
        INNER JOIN aiops_instance ai ON ai.aiopsItemType = #{aiopsItemType} AND aeci.eaiCloudId = ai.aiopsItemId
        INNER JOIN aiops_device_collect_detail adcd ON ai.id = adcd.aiId
        INNER JOIN aiops_collect_config acc on acc.id = adcd.accid
        WHERE acc.uploadDataModelCode = #{uploadDataModelCode} AND aeci.eaiType = #{eaiType}
        <if test="serviceCode != null and serviceCode != ''">
            AND aeci.serviceCode = #{serviceCode}
        </if>
    </select>

    <select id="selectAiopsEaiCloudAdcwIdList" resultType="java.lang.Long">
        SELECT adcw.id AS adcwId
        FROM aiops_eai_cloud_instance aeci
        INNER JOIN aiops_instance ai ON ai.aiopsItemType = #{aiopsItemType} AND aeci.eaiCloudId = ai.aiopsItemId
        INNER JOIN aiops_device_collect_detail adcd ON ai.id = adcd.aiId
        INNER JOIN aiops_device_collect_warning adcw ON adcd.id = adcw.adcdId
        WHERE aeci.serviceCode = #{serviceCode} AND aeci.eaiType = #{eaiType}
    </select>

    <delete id="removeAiopsEaiCloudInstanceDetail">
        DELETE ai, aeci
        FROM aiops_instance ai
        INNER JOIN aiops_eai_cloud_instance aeci ON aeci.eaiCloudId = ai.aiopsItemId
        WHERE aeci.serviceCode = #{serviceCode} AND aeci.eaiType = #{eaiType}
    </delete>

    <select id="getModuleCollectDetail" resultType="com.digiwin.escloud.aioitms.instance.model.AiopsModuleCollect">
        SELECT DISTINCT amcm.samcdId, amcm.samclId, amcm.accId, acc.collectCode, acc.collectName, acw.id acwId, acw.warningCode
        FROM aiops_module_collect_mapping amcm
        LEFT JOIN supplier_aiops_module_collect_layered as samcl on samcl.id = amcm.samclId
        LEFT JOIN supplier_aiops_module_class_detail as samcd on samcd.id = samcl.samcdId AND amcm.samcdId = samcd.id
        LEFT JOIN aiops_collect_config acc ON acc.id = amcm.accId AND acc.scopeId = 'DefaultConfig'
        LEFT JOIN aiops_collect_warning acw ON acw.accId = acc.id AND acw.scopeId = 'DefaultConfig'

        WHERE acc.id IS NOT NULL
        <if test="eaiType != null and eaiType != ''">
            AND samcd.aiopsItem = #{eaiType}
        </if>
        order by acc.collectCode asc
    </select>

    <select id="getInstanceCollectDetail" resultType="com.digiwin.escloud.aioitms.instance.model.AiopsInstanceCollectMapping">
        SELECT ai.id aiId, aeci.serviceCode, adcd.id adcdId, acc.collectCode, acc.collectName, acw.id acwId, acw.warningCode, acw.warningName
        FROM aiops_eai_cloud_instance aeci
        LEFT JOIN aiops_instance ai ON ai.aiopsItemId = aeci.eaiCloudId
        LEFT JOIN aiops_device_collect_detail adcd ON adcd.aiId = ai.id
        LEFT JOIN aiops_collect_config acc ON acc.id = adcd.accId
        LEFT JOIN aiops_device_collect_warning adcw ON adcw.adcdId = adcd.id
        LEFT JOIN aiops_collect_warning acw ON acw.id = adcw.acwId

        WHERE 1=1
        <if test="eaiType != null and eaiType != ''">
            AND aeci.eaiType = #{eaiType}
        </if>
        <if test="serviceCode != null and serviceCode != ''">
            AND aeci.serviceCode = #{serviceCode}
        </if>
        ORDER BY aeci.serviceCode ASC;
    </select>

    <insert id="batchAddNewInstanceCollectDetail" parameterType="java.util.Map">
        INSERT INTO aiops_device_collect_detail(id, accId, collectName, aiId)
        VALUES
        <if test="mapList != null">
            <foreach collection="mapList" item="map" separator=",">
                (#{map.id}, #{map.accId}, #{map.collectName}, #{map.aiId})
            </foreach>
        </if>
    </insert>

    <insert id="batchAddNewInstanceWarningDetail" parameterType="java.util.Map">
        INSERT INTO aiops_device_collect_warning(id, adcdId, acwId)
        VALUES
        <if test="mapList != null">
            <foreach collection="mapList" item="map" separator=",">
                (#{map.id}, #{map.adcdId}, #{map.acwId})
            </foreach>
        </if>
    </insert>

    <select id="selectUploadBigDataInfoByMap"
            resultType="com.digiwin.escloud.aioitms.model.bigdata.UploadBigDataContext">
        SELECT ai.eid, IFNULL(adim.deviceId, '') AS deviceId, adcd.accId, adcd.id AS adcdId,
               acc.uploadDataModelCode, ai.aiopsItemId
        FROM aiops_instance ai
        INNER JOIN aiops_device_collect_detail adcd ON ai.id = adcd.aiId
        <if test="adcdIdList != null">
            <foreach collection="adcdIdList" item="item" open=" AND adcd.id IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        INNER JOIN aiops_collect_config acc ON adcd.accId = acc.id
        <if test="accId != null and accId > 0">
            AND acc.id = #{accId}
        </if>
        <if test="collectCode != null and collectCode != ''">
            AND acc.collectCode = #{collectCode}
        </if>
        LEFT JOIN aiops_device_instance_mapping adim ON ai.id = adim.aiId AND adcd.adimId = adim.id
        <where>
            <if test="aiopsItemIdList != null">
                <foreach collection="aiopsItemIdList" item="item" open=" OR ai.aiopsItemId IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectTrustDetailByMap" resultType="java.util.Map">
        SELECT ai.id AS aiId, ai.aiopsItemType, ai.aiopsItem, ai.aiopsItemId,
        <choose>
            <when test="aiopsItemType == 'DEVICE'">
                ad.id, ad.eid, ad.deviceId, ad.deviceName, ad.platform, ad.ipAddress, ad.placementPoint, ad.remark,
                ad.aiopskitVersion, ad.escliVersion, ad.registerTime, ad.lastCheckInTime, ad.isDeleted,
                adtm.deviceType,
            </when>
            <when test="aiopsItemType == 'DATABASE'">
                `add`.id, `add`.adId, `add`.deviceId, `add`.dbId, `add`.dbType, `add`.dbDisplayName, `add`.dbIpAddress,
                `add`.dbPort, `add`.dbServerName, `add`.dbInstanceName, `add`.isDelete,
            </when>
            <when test="aiopsItemType == 'SNMP'">
                asi.id, asi.eid, asi.snmpType, asi.snmpId, asi.snmpName, asi.snmpIpAddress, asi.snmpPort,
                asi.snmpPlacementPoint, asi.snmpRemark, asi.snmpCustomerNotes, asi.snmpVersion, asi.snmpCommunity, asi.snmpContextName,
                asi.snmpSecurityLevel, asi.snmpUserName, asi.snmpAuthProtocol, asi.snmpAuthPassword,
                asi.snmpPrivProtocol, asi.snmpPrivPassword, asi.snmpIpAddress as snmpIp,
            </when>
            <when test="aiopsItemType == 'PRODUCT_APP'">
                adpm.id, adpm.apaId, adpm.adId, adpm.deviceId, adpm.appId,
            </when>
            <when test="aiopsItemType == 'BACKUP'">
                absi.id, absi.eid, absi.deviceId, absi.backupSoftwareType, absi.backupSoftwareDisplayName,
                absi.backupSoftwareId,
            </when>
            <when test="aiopsItemType == 'TMP_RH'">
                atri.id, atri.sid, atri.eid, atri.customer_service_code, atri.thiid, atri.tmp_rh_id, atri.device_serial,
                atri.device_name, atri.device_status, atri.authorize, atri.scrapped_time, atri.create_time,
                atri.customer_use_device_time,
            </when>
            <when test="aiopsItemType == 'EDR'">
                aei.id, aei.edrServerId, aei.edrOrgId, aei.edrOrgName, aei.edrDeviceId, aei.edrId,
                aei.edrDeviceIpAddress, aei.edrDeviceName,
            </when>
            <when test="aiopsItemType == 'HTTP'">
                ahi.id, ahi.eid, ahi.apiCollectType, ahi.apiCollectId, ahi.apiDeviceName,
                ahi.apiDevicePlace, ahi.apiRemark, ahi.apiCustomerNotes, ahi.ip, ahi.apiAddress, ahi.apiBasicLoginId, ahi.apiBasicLoginPwd,
                ahi.apiBasicAuthCode,
            </when>
            <when test="aiopsItemType == 'SMART_METER'">
                asmi.id, asmi.eid, asmi.smartMeterDeviceId, asmi.smartMeterDeviceName, asmi.smartMeterId,
                asmi.lastCheckInTime,
            </when>
        </choose>
               ai2.name AS aiopsItemName, ai2.name_CN AS aiopsItemName_CN, ai2.name_TW AS aiopsItemName_TW,
               ait.name AS aiopsItemTypeName, ait.name_CN AS aiopsItemTypeName_CN, ait.name_TW AS aiopsItemTypeName_TW
        FROM aiops_instance ai
        <choose>
            <when test="aiopsItemType == 'DEVICE'">
                INNER JOIN aiops_device ad ON ai.aiopsItemType = #{aiopsItemType}
                    AND ai.aiopsItemId = ad.deviceId
                LEFT JOIN aiops_device_type_mapping adtm ON ad.id = adtm.adId
            </when>
            <when test="aiopsItemType == 'DATABASE'">
                INNER JOIN aiops_device_datasource `add` ON ai.aiopsItemType = #{aiopsItemType}
                    AND ai.aiopsItemId = `add`.dbId
            </when>
            <when test="aiopsItemType == 'SNMP'">
                INNER JOIN aiops_snmp_instance asi ON ai.aiopsItemType = #{aiopsItemType}
                    AND ai.aiopsItemId = asi.snmpId
            </when>
            <when test="aiopsItemType == 'PRODUCT_APP'">
                INNER JOIN aiops_device_product_mapping adpm ON ai.aiopsItemType = #{aiopsItemType}
                    AND ai.aiopsItemId = adpm.appId
            </when>
            <when test="aiopsItemType == 'BACKUP'">
                INNER JOIN aiops_backup_software_instance absi ON ai.aiopsItemType = #{aiopsItemType}
                    AND ai.aiopsItemId = absi.backupSoftwareId
            </when>
            <when test="aiopsItemType == 'TMP_RH'">
                INNER JOIN aiops_temp_rh_instance atri ON ai.aiopsItemType = #{aiopsItemType}
                    AND ai.aiopsItemId = atri.tmp_rh_id
            </when>
            <when test="aiopsItemType == 'EDR'">
                INNER JOIN aiops_edr_instance aei ON ai.aiopsItemType = #{aiopsItemType}
                    AND ai.aiopsItemId = aei.edrId
            </when>
            <when test="aiopsItemType == 'HTTP'">
                INNER JOIN aiops_http_instance ahi ON ai.aiopsItemType = #{aiopsItemType}
                    AND ai.aiopsItemId = ahi.apiCollectId
            </when>
            <when test="aiopsItemType == 'SMART_METER'">
                INNER JOIN aiops_smart_meter_instance asmi ON ai.aiopsItemType = #{aiopsItemType}
                    AND ai.aiopsItemId = asmi.smartMeterId
            </when>
        </choose>
        LEFT JOIN aiops_item ai2 ON ai.aiopsItem = ai2.code
        LEFT JOIN aiops_item_type ait ON ai.aiopsItemType = ait.code
        <where>
            <if test="aiopsItemType != null and aiopsItemType != ''">
                AND ai.aiopsItemType = #{aiopsItemType}
            </if>
            <if test="aiopsItemId != null and aiopsItemId != ''">
                AND ai.aiopsItemId = #{aiopsItemId}
            </if>
            <if test="aiId != null and aiId > 0">
                AND ai.id = #{aiId}
            </if>
        </where>
    </select>

    <select id="selectReportDeviceAppInstanceInfo" resultType="java.util.Map">
        SELECT ad.deviceName AS instanceName, ad.lastCheckInTime >= DATE_SUB(now(), INTERVAL 3 DAY) AS isOnLine,
               ad.deviceId, adim2.aiId,
               IFNULL(pInfo.modelCode, '') AS modelCode, pInfo.productAiId
              , ad.platform
        FROM aiops_device ad
        LEFT JOIN (
            SELECT adim.adId, apa.modelCode, adim.aiId AS productAiId
            FROM aiops_device_instance_mapping adim
            INNER JOIN aiops_instance ai ON adim.aiId = ai.id AND ai.aiopsItemType = 'PRODUCT_APP'
                AND (ai.aiopsItem = #{productCode} OR ai.aiopsItem = #{oriProductCode})
            INNER JOIN aiops_device_product_mapping adpm ON adim.adId = adpm.adId AND adpm.appId = ai.aiopsItemId
            INNER JOIN aiops_product_app apa ON adpm.apaId = apa.id
        ) pInfo ON ad.id = pInfo.adId
        INNER JOIN aiops_device_instance_mapping adim2 ON ad.id = adim2.adId
        INNER JOIN aiops_instance ai2 ON ad.eid = ai2.eid AND adim2.aiId = ai2.id
            AND ai2.aiopsItemType = 'DEVICE' AND ad.deviceId = ai2.aiopsItemId
        WHERE ad.eid = #{eid}
        <if test="deviceIdList != null and deviceIdList.size > 0">
            <foreach collection="deviceIdList" item="item" open=" AND ad.deviceId IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectDatabaseInstanceCount" resultType="Integer">
        SELECT COUNT(1) FROM aiops_device_datasource `add`
        WHERE `add`.dbId = #{dbId} AND `add`.dbHostDevice = 1
        <if test="deviceIdList != null">
            <foreach collection="deviceIdList" item="item" open=" AND `add`.deviceId IN(" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <sql id="selectDatabaseInstanceWithCondition" >
        SELECT dbId,
        CASE WHEN dbType = 'mssql_v2' THEN
        CASE WHEN IFNULL(dbInstanceName, '') = '' THEN IFNULL(dbIpAddress, '')
        ELSE CONCAT(IFNULL(dbIpAddress, ''), '\\', IFNULL(dbInstanceName, ''))
        END
        WHEN dbType = 'oracle' THEN
        CONCAT(IFNULL(dbIpAddress, ''), ':', IFNULL(dbPort, ''), '\\', IFNULL(dbServerName, ''))
        ELSE CASE WHEN IFNULL(dbPort, '') = '' THEN IFNULL(dbIpAddress, '')
        ELSE CONCAT(IFNULL(dbIpAddress, ''), ':', IFNULL(dbPort, ''))
        END
        END AS instanceName,
        ad.deviceId, ad.deviceName,
        ai.id AS aiId, (`add`.dbIpAddress = ad.ipAddress) AS ipMatch
        , `add`.dbtype, ad.platform
        FROM aiops_device_datasource `add`
        INNER JOIN aiops_device ad ON `add`.adId = ad.id
        INNER JOIN aiops_instance ai ON ai.aiopsItemType = 'DATABASE' AND `add`.dbId = ai.aiopsItemId AND ai.eid = #{eid}
        WHERE `add`.dbId = #{dbId} AND `add`.dbHostDevice = 1
        <if test="deviceIdList != null">
            <foreach collection="deviceIdList" item="item" open=" AND `add`.deviceId IN(" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY ipMatch DESC
        LIMIT 1
    </sql>

    <sql id="selectDatabaseInstanceWithoutCondition">
        SELECT dbId,
        CASE WHEN dbType = 'mssql_v2' THEN
        CASE WHEN IFNULL(dbInstanceName, '') = '' THEN IFNULL(dbIpAddress, '')
        ELSE CONCAT(IFNULL(dbIpAddress, ''), '\\', IFNULL(dbInstanceName, ''))
        END
        WHEN dbType = 'oracle' THEN
        CONCAT(IFNULL(dbIpAddress, ''), ':', IFNULL(dbPort, ''), '\\', IFNULL(dbServerName, ''))
        ELSE CASE WHEN IFNULL(dbPort, '') = '' THEN IFNULL(dbIpAddress, '')
        ELSE CONCAT(IFNULL(dbIpAddress, ''), ':', IFNULL(dbPort, ''))
        END
        END AS instanceName,
        ad.deviceId, ad.deviceName,
        ai.id AS aiId, (`add`.dbIpAddress = ad.ipAddress) AS ipMatch
        , `add`.dbtype, ad.platform
        FROM aiops_device_datasource `add`
        INNER JOIN aiops_device ad ON `add`.adId = ad.id
        INNER JOIN aiops_instance ai ON ai.aiopsItemType = 'DATABASE' AND `add`.dbId = ai.aiopsItemId AND ai.eid = #{eid}
        WHERE `add`.dbId = #{dbId}
        <if test="deviceIdList != null">
            <foreach collection="deviceIdList" item="item" open=" AND `add`.deviceId IN(" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY ipMatch DESC
        LIMIT 1
    </sql>

    <select id="selectReportDbInstanceInfo" resultType="java.util.Map">
        <choose>
            <when test="count > 0">
                <include refid="selectDatabaseInstanceWithCondition"/>
            </when>
            <otherwise>
                <include refid="selectDatabaseInstanceWithoutCondition"/>
            </otherwise>
        </choose>
    </select>

    <select id="selectCategoryNamesByAiopsItems" resultType="java.lang.String" parameterType="list">
        SELECT categoryNumber
        FROM asset_category
        WHERE aiopsItem IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectMergeStatisticsDetailByMap" resultMap="mergeStatisticsDetailInfoMap">
        SELECT main.*,main.id aiopsInstanceId,
               adim.*,
               aigm.aiopsItemGroup
        FROM (
            SELECT <include refid="getAiopsInstanceWithNoIpDisplayNameSql">
                        <property name="aiAlias" value="ai"/>
                   </include> AS deviceName,
                   <include refid="getAiopsInstancePlacePointSql">
                       <property name="aiAlias" value="ai"/>
                   </include> AS placementPoint,
                   <include refid="getAiopsInstanceIpAddressSql">
                       <property name="aiAlias" value="ai"/>
                   </include> AS ipAddress,
                   ai.id, ai.eid, ai.aiopsItemType, ai.aiopsItem, ai.aiopsItemId,
                   IFNULL(ai.aiopsAuthStatus, 'UNAUTH') AS aiopsAuthStatus
            FROM aiops_instance ai
            WHERE 1=1
            <if test="eid != null">
               AND ai.eid = #{eid}
            </if>
            <if test="samcdIdList != null and samcdIdList.size() > 0">
                <foreach collection="samcdIdList" item="item" open=" AND ai.samcdId IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
        ) main
        <include refid="getJoinAiopskitDeviceSql">
            <property name="aiAlias" value="main"/>
        </include>
        <include refid="getJoinAiopsItemGroupSql">
            <property name="aiAlias" value="main"/>
        </include>
        <where>
            <if test="deviceNameOrId != null and deviceNameOrId != ''">
                AND (main.deviceName LIKE CONCAT('%', #{deviceNameOrId}, '%')
                OR main.aiopsItemId LIKE CONCAT('%', #{deviceNameOrId}, '%'))
            </if>
            <if test="placementPoint != null and placementPoint != ''">
                AND main.placementPoint LIKE CONCAT('%', #{placementPoint}, '%')
            </if>
            <if test="ipAddress != null and ipAddress != ''">
                AND main.ipAddress = #{ipAddress}
            </if>
            <if test="aiopsAuthStatusList != null">
                <foreach collection="aiopsAuthStatusList" item="item"
                         open=" AND main.aiopsAuthStatus IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="aiopsItemList != null  and aiopsItemList.size() > 0">
                <foreach collection="aiopsItemList" item="item"
                         open=" AND main.aiopsItem IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="instanceIdList != null  and instanceIdList.size() > 0">
                <foreach collection="instanceIdList" item="item"
                         open=" AND main.aiopsItemId not IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="aiIdList != null  and aiIdList.size() > 0">
                <foreach collection="aiIdList" item="item"
                         open=" AND main.id IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="notInAiIdList != null  and notInAiIdList.size() > 0">
                <foreach collection="notInAiIdList" item="item"
                         open=" AND main.id NOT IN (" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <!--考虑(日后场景)代理主机可能会有多台，因此在限制代理设备时，只能透过aiId去约束-->
            <if test="(agentDeviceNameOrPlacementPoint != null and agentDeviceNameOrPlacementPoint != '') or
                  (platformList != null and platformList.size() > 0)">
                AND EXISTS (
                    SELECT 1
                    FROM aiops_device ad
                    LEFT JOIN aiops_device_type_mapping adtm ON ad.id = adtm.adId
                    LEFT JOIN aiops_device_instance_mapping adim3 ON ad.id = adim3.adId
                    WHERE adim3.aiId = adim.aiId
                    <if test="eid != null">
                        AND ad.eid = #{eid}
                    </if>
                    <if test="agentDeviceNameOrPlacementPoint != null and agentDeviceNameOrPlacementPoint != ''">
                        AND (adim.ad_deviceName LIKE CONCAT('%', #{agentDeviceNameOrPlacementPoint}, '%')
                        OR adim.ad_placementPoint LIKE CONCAT('%', #{agentDeviceNameOrPlacementPoint}, '%'))
                    </if>
                    <if test="platformList != null">
                        <foreach collection="platformList" item="item" open=" AND adim.ad_platform IN (" separator=", " close=")">
                            #{item}
                        </foreach>
                    </if>
                )
            </if>
        </where>
        ORDER BY CASE main.aiopsAuthStatus WHEN 'AUTHED' THEN 1
        WHEN 'NONE' THEN 2
        WHEN 'UNAUTH' THEN 3
        ELSE 4 END, main.deviceName
    </select>

    <select id="selectAiTrustCommonInfoByMap" resultType="java.util.Map">
        SELECT ai.id AS aiId, ai.aiopsItemType, ai.aiopsItem, ai.aiopsItemId,
        <include refid="getAiopsInstanceWithNoIpDisplayNameSql">
            <property name="aiAlias" value="ai"/>
        </include> AS instanceName,
        <include refid="getAiopsInstancePlacePointSql">
            <property name="aiAlias" value="ai"/>
        </include> AS placementPoint,
        <include refid="getAiopsInstanceIpAddressSql">
            <property name="aiAlias" value="ai"/>
        </include> AS ipAddress,
               ai2.name AS aiopsItemName, ai2.name_CN AS aiopsItemName_CN, ai2.name_TW AS aiopsItemName_TW,
               ait.name AS aiopsItemTypeName, ait.name_CN AS aiopsItemTypeName_CN, ait.name_TW AS aiopsItemTypeName_TW
        FROM aiops_instance ai
        LEFT JOIN aiops_item ai2 ON ai.aiopsItem = ai2.code
        LEFT JOIN aiops_item_type ait ON ai.aiopsItemType = ait.code
        <where>
            <if test="aiopsItemType != null and aiopsItemType != ''">
                AND ai.aiopsItemType = #{aiopsItemType}
            </if>
            <if test="aiopsItemId != null and aiopsItemId != ''">
                AND ai.aiopsItemId = #{aiopsItemId}
            </if>
            <if test="aiId != null and aiId > 0">
                AND ai.id = #{aiId}
            </if>
        </where>
    </select>

    <select id="selectAiopsItemListByMap" resultType="java.lang.String">
        SELECT ai.aiopsItem
        FROM aiops_instance ai
        <if test="deviceId != null and deviceId != ''">
            INNER JOIN aiops_device_instance_mapping adim ON ai.id = adim.aiId AND adim.deviceId = #{deviceId}
        </if>
        <where>
            <if test="eid != null and eid > 0">
                AND ai.eid = #{eid}
            </if>
            <if test="aiopsItemIdList != null">
                <foreach collection="aiopsItemIdList" item="item" open=" AND ai.aiopsItemId IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <update id="updateAiopsInstanceTmcdIdByMapList">
        UPDATE aiops_instance
        SET id = id
        <if test="mapList != null">
            <foreach collection="mapList" item="item"
                     open=", tmcdId = (CASE WHEN " separator=" WHEN " close=" END) ">
                (eid = #{item.eid}
                <if test="item.onlyTmcdIdEmpty != null and item.onlyTmcdIdEmpty">
                    AND IFNULL(tmcdId, 0) = 0
                </if>
                <choose>
                    <when test="item.samcdIdList != null and item.samcdIdList.size() > 0">
                        <foreach collection="item.samcdIdList" item="item2"
                                 open=" AND samcdId IN(" separator=", " close="))">
                            #{item2}
                        </foreach>
                    </when>
                    <otherwise>
                        AND FALSE)
                    </otherwise>
                </choose>
                THEN #{item.tmcdId}
            </foreach>
        </if>
        WHERE 1 != 1
        <if test="mapList != null">
            <foreach collection="mapList" item="item"
                     open=" OR ((" separator=") OR (" close=")) ">
                eid = #{item.eid}
                <if test="item.onlyTmcdIdEmpty != null and item.onlyTmcdIdEmpty">
                    AND IFNULL(tmcdId, 0) = 0
                </if>
                <choose>
                    <when test="item.samcdIdList != null and item.samcdIdList.size() > 0">
                        <foreach collection="item.samcdIdList" item="item2"
                                 open=" AND samcdId IN(" separator=", " close=")">
                            #{item2}
                        </foreach>
                    </when>
                    <otherwise>
                        AND FALSE
                    </otherwise>
                </choose>
            </foreach>
        </if>
    </update>

    <select id="selectAiRelateEidList" resultType="java.util.Map">
        SELECT ai.eid
        FROM aiops_instance ai
        <where>
            <if test="aiopsItemType != null and aiopsItemType != ''">
                AND ai.aiopsItemType = #{aiopsItemType}
            </if>
            <if test="aiopsItemTypeList != null">
                <foreach collection="aiopsItemTypeList" item="item"
                         open=" AND ai.aiopsItemType IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="aiopsItem != null and aiopsItem != ''">
                AND ai.aiopsItem = #{aiopsItem}
            </if>
            <if test="aiopsItemList != null">
                <foreach collection="aiopsItemList" item="item"
                         open=" AND ai.aiopsItem IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="aiopsAuthStatusList != null">
                <foreach collection="aiopsAuthStatusList" item="item"
                         open=" AND ai.aiopsAuthStatus IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
            <if test="productAppCodeList != ''">
                <foreach collection="productAppCodeList" item="item"
                         open=" AND EXISTS (SELECT 1 FROM aiops_device_product_mapping adpm
                                        INNER JOIN aiops_product_app apa ON adpm.apaId = apa.id
                                        WHERE adpm.appId = ai.aiopsItemId AND apa.modelCode IN(" separator=", " close="))">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY eid
    </select>

    <select id="selectAiopsInstanceNameByAiopsItemIdList" resultType="java.util.Map">
        SELECT ai.id as aiId, ai.aiopsItemId
        <include refid="getAiopsInstanceDisplayNameSql">
            <property name="aiAlias" value="ai"/>
        </include> AS aiopsInstanceName
        FROM aiops_instance ai
        WHERE 1 != 1
        <foreach collection="aiopsItemIdList" item="item" open=" OR ai.aiopsItemId IN(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectAiAuthedCountByTmcdIdList" resultType="java.util.Map">
        SELECT ai.tmcdId, COUNT(*) AS authedCount
        FROM aiops_instance ai
        WHERE ai.aiopsAuthStatus = 'AUTHED'
        <if test="tmcdIdList != null">
            <foreach collection="tmcdIdList" item="item" open="AND ai.tmcdId IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY ai.tmcdId
    </select>

    <select id="selectAiAuthedCountByAiopsItemIds" resultType="Integer">
        SELECT count(1)
        FROM aiops_instance ai
        WHERE ai.aiopsAuthStatus = 'AUTHED'
        <if test="aiopsItemIdList != null">
            <foreach collection="aiopsItemIdList" item="item" open="AND ai.aiopsItemId IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectAiIdByTmcdId" resultType="Long">
        SELECT ai.id
        FROM aiops_instance ai
        WHERE ai.recoverableAuth = 1 AND aiopsAuthStatus = 'UNAUTH'
        AND ai.tmcdId = #{tmcdId}
        ORDER BY __version__ DESC
        <if test="limit != null">
        LIMIT #{limit}
        </if>
    </select>

    <update id="updateAiAuthRecoverAuth" >
       UPDATE aiops_instance SET aiopsAuthStatus = 'AUTHED' , recoverableAuth = 0
       WHERE id IN
        <foreach collection="aiIdList" item="item" open="(" separator=", " close=")">
            #{item}
        </foreach>
    </update>


    <select id="selectInstanceExecParamsContent" resultType="java.util.Map">
        SELECT oriExecParamsContent, aiopsItem
            FROM aiops_instance
        WHERE aiopsItemId = #{aiopsItemId}
        limit 1
    </select>

    <update id="updateRuleEngineAuthStatus">
        UPDATE aiops_instance a,rule_engine b SET b.authStatus =1
        WHERE a.eid = b.eid AND a.id = b.aiId AND a.aiopsAuthStatus = 'AUTHED' AND b.authStatus = 0
        AND a.id IN
        <foreach collection="aiIdList" item="item" open="(" separator=", " close=")">
            #{item}
        </foreach>
    </update>
    
    
    <select id="queryModuleCodeListByDeviceId" resultType="java.lang.String">
        SELECT b.modelCode
        FROM aiops_device_product_mapping a
                 LEFT JOIN aiops_product_app b ON a.apaId= b.id
        WHERE a.deviceId=#{deviceId}
    </select>

    <select id="selectDeviceIdByAiIdList" resultType="String">
        SELECT deviceId FROM aiops_device_instance_mapping WHERE aiId IN
        <foreach collection="aiIdList" item="item" open="(" separator=", " close=")">
            #{item}
        </foreach>
        GROUP BY deviceId
    </select>

    <select id="selectAiopsTpInstanceByAiopsItemId"
            resultType="com.digiwin.escloud.aioitms.instance.model.AiopsTpModuleInstance">
        SELECT atmi.*,tt.tpAgentDownloadUrl,tt.tpProgram
        FROM aiops_tp_module_instance atmi
        LEFT JOIN tenant_tp tt on tt.tpTenantId = atmi.tpTenantId
        WHERE aiopsItemId = #{aiopsItemId}
        LIMIT 1
    </select>
    <select id="selectAiopsTpInstanceByUniqueKey" resultType="com.digiwin.escloud.aioitms.instance.model.AiopsTpModuleInstance">
        SELECT atmi.*
        FROM aiops_tp_module_instance atmi
        WHERE atmi.deviceId = #{deviceId} AND atmi.eid = #{eid}  AND atmi.tpModule = #{tpModule} AND tpTenantId = #{tpTenantId} AND tpDeviceId = #{tpDeviceId}
    </select>

    <select id="selectTpAiopsItemIdByAtmiId" resultType="java.lang.String">
        SELECT aiopsItemId
        FROM aiops_tp_module_instance
        WHERE id = #{atmiId}
    </select>

    <insert id="batchInsertOrUpdateTpModuleInstance" parameterType="java.util.List">
        INSERT INTO aiops_tp_module_instance
        (id, eid, deviceId, tpTenantId, tpDeviceId, tpModule, tpLastScanTime, aiopsItemId)
        VALUES
        <foreach collection="aiopsTpModuleInstances" item="item" index="index" separator=",">
            (
            #{item.id},
            #{item.eid},
            #{item.deviceId},
            #{item.tpTenantId},
            #{item.tpDeviceId},
            #{item.tpModule},
            #{item.tpLastScanTime},
            #{item.aiopsItemId}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        eid = VALUES(eid),
        deviceId = VALUES(deviceId),
        tpTenantId = VALUES(tpTenantId),
        tpDeviceId = VALUES(tpDeviceId),
        tpModule = VALUES(tpModule),
        tpLastScanTime = VALUES(tpLastScanTime),
        aiopsItemId = VALUES(aiopsItemId)
    </insert>


    <select id="selectDeviceTpInstanceByDeviceIdAndEid" resultType="com.digiwin.escloud.aioitms.instance.model.AiopsTpModuleInstance">
        SELECT atmi.id,atmi.tpTenantId,atmi.tpDeviceId,atmi.tpModule,adim.deviceId   FROM aiops_tp_module_instance atmi
        INNER JOIN aiops_instance ai on ai.aiopsItemId = atmi.aiopsItemId
        LEFT JOIN aiops_device_instance_mapping adim on adim.aiId = ai.id
        WHERE atmi.deviceId = #{deviceId} AND atmi.eid = #{eid}  AND atmi.tpModule = #{tpModule}
    </select>

    <select id="selectAiopsInstanceAiopsItemByDeviceId" parameterType="String" resultType="String">
        SELECT aiopsItem
        FROM aiops_instance
        WHERE aiopsItemId =#{deviceId}
        limit 1
    </select>

    <update id="updateModuleInstanceTpLastScanTime">
        UPDATE aiops_tp_module_instance
        SET tpLastScanTime = #{lastScanTime}
        WHERE tpTenantId = #{tpTenantId} AND tpDeviceId = #{tpDeviceId}
    </update>

    <select id="selectAiopsTpModuleInstance"  resultType="com.digiwin.escloud.aioitms.instance.model.AiopsTpModuleInstance">
        SELECT tpDeviceId, tpLastScanTime
        FROM aiops_tp_module_instance
        WHERE eid = #{eid}
        AND tpTenantId = #{tpTenantId}
        AND tpModule = 'ASIA_VULN'
        AND tpLastScanTime IS NOT NULL
	</select>

    <select id="getTpMaxLastScanTimeByDeviceId" parameterType="String" resultType="String">
        SELECT tpLastScanTime
        FROM aiops_tp_module_instance
        WHERE deviceId = #{deviceId}
        AND tpModule = 'ASIA_VULN'
        limit 1
    </select>
	
    <select id="selectAiopsItemListByEid" resultType="com.digiwin.escloud.aioitms.device.model.DeviceAiopsItem">
        SELECT aiopsItem, samcdId, deviceId, isOnLine
        FROM (
                 SELECT ai.aiopsItem, ai.samcdId, ai.aiopsItemId AS deviceId,
                    IF(ad.lastCheckInTime >= DATE_SUB(NOW(), INTERVAL 3 DAY), TRUE, FALSE) AS isOnLine
                 FROM aiops_instance ai
                 LEFT JOIN  aiops_device ad on ai.eid = ad.eid and ai.aiopsItemId = ad.deviceId
                 WHERE ai.eid = #{eid} AND ai.aiopsAuthStatus != 'INVALID' UNION ALL
                 SELECT adtm.deviceType AS aiopsItem, 0 AS samcdId, ad.deviceId,
                 IF(ad.lastCheckInTime >= DATE_SUB(NOW(), INTERVAL 3 DAY), TRUE, FALSE) AS isOnLine
                 FROM aiops_device ad
                     INNER JOIN aiops_device_type_mapping adtm ON ad.id = adtm.adId
                     LEFT JOIN (
                     SELECT adim2.id, adim2.adId
                     FROM aiops_device_instance_mapping adim2
                     INNER JOIN aiops_instance ai2 ON ai2.eid = #{eid} AND ai2.aiopsItemType = 'DEVICE' AND adim2.aiId = ai2.id
                     ) adim ON ad.id = adim.adId
                 WHERE ad.eid = #{eid} AND adim.id IS NULL AND ad.isDeleted = 0 UNION ALL
                 SELECT 'UNDEFINED' AS aiopsItem, 0 AS samcdId, ad.deviceId,
                 IF(ad.lastCheckInTime >= DATE_SUB(NOW(), INTERVAL 3 DAY), TRUE, FALSE) AS isOnLine
                 FROM aiops_device ad
                     LEFT JOIN aiops_device_type_mapping adtm ON ad.id = adtm.adId
                 WHERE ad.eid =#{eid} AND adtm.id IS NULL AND ad.isDeleted = 0
             ) main
        WHERE main.deviceId is not null
        <if test="aiopsItemList != null">
            <foreach collection="aiopsItemList" item="item" open="AND main.aiopsItem IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <sql id="TpDeviceId">
        SELECT deviceId FROM aiops_device_tp WHERE eid = #{eid}
        UNION ALL
        SELECT deviceId FROM aiops_tp_module_instance WHERE eid = #{eid} AND tpModule = 'ASIA_VULN'
        GROUP BY deviceId
    </sql>
    <select id="selectDeviceByEid" resultType="com.digiwin.escloud.aioitms.device.model.DeviceInfoNew">
        SELECT ad.*,ai.aiopsItem FROM aiops_device ad
        INNER JOIN aiops_instance ai ON ad.deviceId = ai.aiopsItemId
        WHERE ad.eid  = #{eid} AND  ad.deviceId NOT IN (<include refid="TpDeviceId"/>)
        AND ai.aiopsAuthStatus = 'AUTHED'
    </select>
    <select id="selectAsiaDeviceIdByEid" resultType="String">
        SELECT tpDeviceId FROM aiops_device_tp WHERE eid = #{eid}
    </select>

    <select id="selectAiIdListByMap" resultType="java.lang.Long">
        SELECT ai.id
        FROM aiops_instance ai
        <where>
            <if test="aiopsItemId != null and aiopsItemId != ''">
                AND ai.aiopsItemId = #{aiopsItemId}
            </if>
            <if test="aiopsItemIdList != null">
                <foreach collection="aiopsItemIdList" item="item"
                         open=" AND ai.aiopsItemId IN(" separator=", " close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectEidByAiId" resultType="java.lang.Long">
        SELECT eid
        FROM aiops_instance
        WHERE id = #{aiId}
    </select>

    <select id="queryDeviceOnline" resultType="Map">
        SELECT
            ai.aiopsItem,
            ai.id AS aiopsInstanceId
        FROM
            aiops_snmp_instance asi
                LEFT JOIN aiops_instance ai ON asi.snmpId = ai.aiopsItemId
        WHERE
            asi.eid = #{eid}
          AND asi.snmpType IN ( 'NAS', 'FIREWALL', 'ESXI', 'UPS', 'RAID', 'MAC' )
          AND ai.aiopsAuthStatus IN ( 'NONE', 'UNAUTH', 'AUTHED' )
        /**查询IPMI和超融合**/
        <if test="samcdIdList != null and samcdIdList.size() > 0">
            UNION ALL
            SELECT
            ai.aiopsItem,
            ai.id AS aiopsInstanceId
            FROM
            aiops_instance ai
            WHERE
            ai.eid = #{eid}
            AND ai.aiopsAuthStatus IN ( 'NONE', 'AUTHED', 'UNAUTH' )
            <foreach collection="samcdIdList" item="item" open=" AND ai.samcdId IN(" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectHostAndClientOnline" resultType="Map">
        SELECT DISTINCT
            ad.id,
            ad.deviceId,
            ad.lastCheckInTime >= DATE_SUB( now( ), INTERVAL 3 DAY ) AS isOnLine,
            adtm.deviceType as aiopsItem
        FROM
            aiops_device ad
                LEFT JOIN aiops_device_type_mapping adtm ON adtm.adId = ad.id
                LEFT JOIN (
                SELECT
                    adId,
                    IFNULL( ai.aiopsAuthStatus, '' ) AS aiopsAuthStatus
                FROM
                    aiops_device_instance_mapping
                        INNER JOIN aiops_instance ai ON ai.aiopsItemType = 'DEVICE'
                        AND ai.eid = #{eid}
                        AND aiId = ai.id
            ) adim ON ad.id = adim.adId
        WHERE
             ad.eid = #{eid}
          AND adtm.deviceType IN ( 'HOST', 'CLIENT' )
          AND IFNULL( adim.aiopsAuthStatus, 'UNAUTH' ) IN ( 'NONE', 'AUTHED', 'UNAUTH' )
    </select>

    <!-- 10766【後端API】【Athena稳态自动更新】稳态自动更新的授权起讫同步逻辑调整，支持稳态根据敏态的合约管控自动更新 -->
    <insert id="batchInsertOrUpdateAiopsAppAutoUpdateInstance" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO aiops_app_auto_update_instance (id, eid, deviceId, aiopsItem, aiopsItemId)
        <foreach collection="aiopsAppAutoUpdateInstanceList" item="item" open=" VALUES(" separator="), (" close=")">
            #{item.id}, #{item.eid}, #{item.deviceId}, #{item.aiopsItem}, #{item.aiopsItemId}
        </foreach>
        ON DUPLICATE KEY UPDATE
          eid = VALUES (eid),
          deviceId = VALUES (deviceId),
          aiopsItem = VALUES (aiopsItem),
          aiopsItemId = VALUES (aiopsItemId);
    </insert>

    <!-- 10766【後端API】【Athena稳态自动更新】稳态自动更新的授权起讫同步逻辑调整，支持稳态根据敏态的合约管控自动更新 -->
    <select id="selectExistAppAutoUpdateInstances" resultType="com.digiwin.escloud.aioitms.instance.model.AiopsAppAutoUpdateInstance">
        select a.*
        from aiops_app_auto_update_instance a
        where 1=1
        <if test="aiopsAppAutoUpdateInstanceList != null">
            <foreach collection="aiopsAppAutoUpdateInstanceList" item="item" open=" AND ( " separator=") or (" close=")">
                a.eid = #{item.eid} and a.deviceId = #{item.deviceId}
            </foreach>
        </if>
    </select>

    <!-- 10766【後端API】【Athena稳态自动更新】稳态自动更新的授权起讫同步逻辑调整，支持稳态根据敏态的合约管控自动更新 -->
    <select id="selectAiopsAppAutoUpdateInstanceByAiPsItemId" resultType="com.digiwin.escloud.aioitms.instance.model.AiopsAppAutoUpdateInstance">
        SELECT *
        FROM aiops_app_auto_update_instance
        WHERE aiopsItemId = #{aiopsItemId}
    </select>

    <select id="selectAppAutoUpdateAioPsItemIdByAsiId" resultType="java.lang.String">
        SELECT aiopsItemId
        FROM aiops_app_auto_update_instance
        WHERE id = #{asiId}
    </select>

    <select id="selectItemType" resultType="String">
        SELECT DISTINCT aiopsItemType FROM aiops_instance where aiopsItem = #{aiopsItem}  LIMIT 1
    </select>

    <select id="selectTrustDetailByAiIdList" resultType="Map">
        SELECT ai.id AS aiId, ai.aiopsItemType, ai.aiopsItem, ai.aiopsItemId,
        <choose>
            <when test="aiopsItemType == 'DEVICE'">
                ad.id, ad.eid, ad.deviceId, ad.deviceName, ad.platform, ad.ipAddress, ad.placementPoint, ad.remark,
                ad.aiopskitVersion, ad.escliVersion, ad.registerTime, ad.lastCheckInTime, ad.isDeleted,
                adtm.deviceType,
            </when>
            <when test="aiopsItemType == 'DATABASE'">
                `add`.id, `add`.adId, `add`.deviceId, `add`.dbId, `add`.dbType, `add`.dbDisplayName, `add`.dbIpAddress,
                `add`.dbPort, `add`.dbServerName, `add`.dbInstanceName, `add`.isDelete,
            </when>
            <when test="aiopsItemType == 'SNMP'">
                asi.id, asi.eid, asi.snmpType, asi.snmpId, asi.snmpName, asi.snmpIpAddress, asi.snmpPort,
                asi.snmpPlacementPoint, asi.snmpRemark, asi.snmpCustomerNotes, asi.snmpVersion, asi.snmpCommunity, asi.snmpContextName,
                asi.snmpSecurityLevel, asi.snmpUserName, asi.snmpAuthProtocol, asi.snmpAuthPassword,
                asi.snmpPrivProtocol, asi.snmpPrivPassword, asi.snmpIpAddress as snmpIp,
            </when>
            <when test="aiopsItemType == 'PRODUCT_APP'">
                adpm.id, adpm.apaId, adpm.adId, adpm.deviceId, adpm.appId,
            </when>
            <when test="aiopsItemType == 'BACKUP'">
                absi.id, absi.eid, absi.deviceId, absi.backupSoftwareType, absi.backupSoftwareDisplayName,
                absi.backupSoftwareId,
            </when>
            <when test="aiopsItemType == 'TMP_RH'">
                atri.id, atri.sid, atri.eid, atri.customer_service_code, atri.thiid, atri.tmp_rh_id, atri.device_serial,
                atri.device_name, atri.device_status, atri.authorize, atri.scrapped_time, atri.create_time,
                atri.customer_use_device_time,
            </when>
            <when test="aiopsItemType == 'EDR'">
                aei.id, aei.edrServerId, aei.edrOrgId, aei.edrOrgName, aei.edrDeviceId, aei.edrId,
                aei.edrDeviceIpAddress, aei.edrDeviceName,
            </when>
            <when test="aiopsItemType == 'HTTP'">
                ahi.id, ahi.eid, ahi.apiCollectType, ahi.apiCollectId, ahi.apiDeviceName,
                ahi.apiDevicePlace, ahi.apiRemark, ahi.apiCustomerNotes, ahi.ip, ahi.apiAddress, ahi.apiBasicLoginId, ahi.apiBasicLoginPwd,
                ahi.apiBasicAuthCode,
            </when>
            <when test="aiopsItemType == 'SMART_METER'">
                asmi.id, asmi.eid, asmi.smartMeterDeviceId, asmi.smartMeterDeviceName, asmi.smartMeterId,
                asmi.lastCheckInTime,
            </when>
        </choose>
        ai2.name AS aiopsItemName, ai2.name_CN AS aiopsItemName_CN, ai2.name_TW AS aiopsItemName_TW,
        ait.name AS aiopsItemTypeName, ait.name_CN AS aiopsItemTypeName_CN, ait.name_TW AS aiopsItemTypeName_TW
        FROM aiops_instance ai
        <choose>
            <when test="aiopsItemType == 'DEVICE'">
                INNER JOIN aiops_device ad ON ai.aiopsItemType = #{aiopsItemType}
                AND ai.aiopsItemId = ad.deviceId
                LEFT JOIN aiops_device_type_mapping adtm ON ad.id = adtm.adId
            </when>
            <when test="aiopsItemType == 'DATABASE'">
                INNER JOIN aiops_device_datasource `add` ON ai.aiopsItemType = #{aiopsItemType}
                AND ai.aiopsItemId = `add`.dbId
            </when>
            <when test="aiopsItemType == 'SNMP'">
                INNER JOIN aiops_snmp_instance asi ON ai.aiopsItemType = #{aiopsItemType}
                AND ai.aiopsItemId = asi.snmpId
            </when>
            <when test="aiopsItemType == 'PRODUCT_APP'">
                INNER JOIN aiops_device_product_mapping adpm ON ai.aiopsItemType = #{aiopsItemType}
                AND ai.aiopsItemId = adpm.appId
            </when>
            <when test="aiopsItemType == 'BACKUP'">
                INNER JOIN aiops_backup_software_instance absi ON ai.aiopsItemType = #{aiopsItemType}
                AND ai.aiopsItemId = absi.backupSoftwareId
            </when>
            <when test="aiopsItemType == 'TMP_RH'">
                INNER JOIN aiops_temp_rh_instance atri ON ai.aiopsItemType = #{aiopsItemType}
                AND ai.aiopsItemId = atri.tmp_rh_id
            </when>
            <when test="aiopsItemType == 'EDR'">
                INNER JOIN aiops_edr_instance aei ON ai.aiopsItemType = #{aiopsItemType}
                AND ai.aiopsItemId = aei.edrId
            </when>
            <when test="aiopsItemType == 'HTTP'">
                INNER JOIN aiops_http_instance ahi ON ai.aiopsItemType = #{aiopsItemType}
                AND ai.aiopsItemId = ahi.apiCollectId
            </when>
            <when test="aiopsItemType == 'SMART_METER'">
                INNER JOIN aiops_smart_meter_instance asmi ON ai.aiopsItemType = #{aiopsItemType}
                AND ai.aiopsItemId = asmi.smartMeterId
            </when>
        </choose>
        LEFT JOIN aiops_item ai2 ON ai.aiopsItem = ai2.code
        LEFT JOIN aiops_item_type ait ON ai.aiopsItemType = ait.code
        <where>
            <if test="aiopsItemType != null and aiopsItemType != ''">
                AND ai.aiopsItemType = #{aiopsItemType}
            </if>
            <if test="aiIdList != null and aiIdList.size() > 0">
                <foreach collection="aiIdList" open=" AND ai.id IN (" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectAiopsKitDeviceList" resultType="com.digiwin.escloud.aioitms.device.model.DeviceInstanceInfo">
        SELECT adim2.aiId,
               ad.id AS id,
               ad.eid AS eid,
               ad.deviceId AS deviceId,
               ad.deviceName AS deviceName,
               ad.platform AS platform,
               ad.ipAddress AS ipAddress,
               ad.placementPoint AS placementPoint,
               ad.remark AS remark,
               ad.aiopskitVersion AS aiopskitVersion,
               ad.escliVersion AS escliVersion,
               ad.registerTime AS registerTime,
               ad.lastCheckInTime AS lastCheckInTime,
               ad.isDeleted AS isDeleted,
               ad.lastCheckInTime >= DATE_SUB(now(), INTERVAL 3 DAY) AS isOnLine,
               TIMESTAMPDIFF(SECOND, ad.lastCheckInTime, now()) AS lastCheckInTimeDifferSecond
        FROM aiops_device_instance_mapping adim2
                 INNER JOIN aiops_device ad ON adim2.adId = ad.id AND ad.eid = #{eid}
                 LEFT JOIN aiops_device_type_mapping adtm ON ad.id = adtm.adId
        WHERE 1=1
        <if test="aiIdList != null and aiIdList.size() > 0">
            <foreach collection="aiIdList" open=" AND adim2.aiId IN (" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>


    <select id="selectEdrServerIdList" resultType="com.digiwin.escloud.aiobasic.edr.model.edr.EdrDevice">
        select aei.edrServerId as serverId, aei.edrDeviceId from aiops_edr_instance aei
                                        inner join aiops_instance ai on ai.aiopsItemId = aei.edrId
                                        inner join aiops_device_instance_mapping adim on adim.aiId = ai.id
                                        inner join aiops_device ad on ad.id = adim.adId
        where ad.deviceId = #{deviceId} and aei.edrServerId is not null
        order by aei.__version__ desc
    </select>

    <select id="selectAiopsItemByGroupCode" resultType="com.digiwin.escloud.aioitms.instance.model.AiopsItemExtend">
        SELECT DISTINCT main.code as code,main.name FROM (
        SELECT ai.aiopsItem as code,ai2.name,ai2.id FROM aiops_item_group_mapping aigm
        LEFT JOIN aiops_instance ai on aigm.aiopsItem = ai.aiopsItem
        LEFT JOIN aiops_item ai2 on ai.aiopsItem = ai2.code
        WHERE ai.eid = #{eid}
        <if test="aiopsItemGroup != null and aiopsItemGroup != ''">
            AND aigm.aiopsItemGroup = #{aiopsItemGroup}
        </if>
        <if test="aiopsItemList != null and aiopsItemList.size() > 0">
            <foreach collection="aiopsItemList" open=" AND ai.aiopsItem IN (" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="aiopsAuthStatusList != null">
            <foreach collection="aiopsAuthStatusList" item="item"
                     open=" AND ai.aiopsAuthStatus IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
        
        <if test="aiopsItemGroup == null">
        UNION ALL
        SELECT ai.aiopsItem as code,ai2.name,ai2.id
        from aiops_instance ai
        LEFT JOIN aiops_item ai2 on ai.aiopsItem = ai2.code
        WHERE ai.eid = #{eid}
        AND ai.aiopsItem IN (
        'TMP_RH'
        )
        </if>
        )main

        ORDER BY main.id

    </select>

    <select id="selectAiopsItemTypeByAiopsItem" resultType="java.lang.String">
        SELECT aiopsItemType
        FROM supplier_aiops_module_class_detail
        WHERE aiopsItem = #{aiopsItem}
    </select>

    <select id="getEidByServiceCode" parameterType="java.lang.String" resultType="java.lang.Long">
        SELECT eid
        FROM supplier_tenant_map
        WHERE serviceCode = #{serviceCode}
    </select>

    <select id="selectAccByAiopsItem" resultType="com.digiwin.escloud.aioitms.collectconfig.model.CollectConfig">
        SELECT acc.id,acc.collectCode,acc.collectName,ai.aiopsItem,item.name AS aiopsItemName,acs.sinkName,acc.uploadDataModelCode
        FROM aiops_device_collect_detail adcd
        LEFT JOIN aiops_collect_config acc ON acc.id= adcd.accId
        LEFT JOIN aiops_instance ai ON ai.id= adcd.aiId
        LEFT JOIN aiops_item item ON item.code = ai.aiopsItem
        LEFT JOIN aiops_collect_sink acs ON acs.modelCode = acc.uploadDataModelCode AND acs.dataScene = 'DATA_UPLOAD_SR'
        WHERE ai.aiopsAuthStatus != 'INVALID' AND acs.sinkName IS NOT NULL AND acs.sinkName != ''
        <if test="aiopsItemList != null">
            <foreach collection="aiopsItemList" item="item" open=" AND ai.aiopsItem IN (" separator=", " close=")">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>